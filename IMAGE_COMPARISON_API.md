# Image Comparison API

A comprehensive image comparison service similar to VisualPing, built into the BargainHawk scraper backend.

## Features

- **Multiple Comparison Algorithms**: Combines pixel-by-pixel, structural similarity (SSIM), histogram analysis, and perceptual hashing
- **Visual Difference Maps**: Generates highlighted images showing exactly what changed
- **Flexible Thresholds**: Configurable similarity thresholds for different use cases
- **Performance Optimized**: Automatically resizes images for optimal comparison speed
- **Multiple Output Formats**: Support for PNG, JPEG, and other common formats

## API Endpoints

### 1. Full Image Comparison

```
GET /api/public/image-comparison/compare
```

**Parameters:**
- `image1Url` (required): URL of the first image
- `image2Url` (required): URL of the second image  
- `includeDifferenceMap` (optional): Whether to include difference map URL (default: false)

**Example:**
```
GET /api/public/image-comparison/compare?image1Url=https://example.com/image1.jpg&image2Url=https://example.com/image2.jpg&includeDifferenceMap=true
```

**Response:**
```json
{
  "overallSimilarity": 0.87,
  "pixelSimilarity": 0.85,
  "structuralSimilarity": 0.89,
  "histogramSimilarity": 0.88,
  "perceptualHashSimilarity": 0.86,
  "hasDifferences": true,
  "significantDifferences": false,
  "image1Dimensions": {"width": 1920, "height": 1080},
  "image2Dimensions": {"width": 1920, "height": 1080},
  "comparisonDimensions": {"width": 1000, "height": 563},
  "comparisonTimestamp": "2024-01-15T10:30:00",
  "similarityPercentage": "87.00%",
  "assessment": "Images are similar with noticeable differences",
  "detailedBreakdown": "Overall: 87.00% | Pixel: 85.00% | Structural: 89.00% | Histogram: 88.00% | Perceptual: 86.00%",
  "differenceMapUrl": "/api/public/image-comparison/difference-map?image1=..."
}
```

### 2. Quick Similarity Check

```
GET /api/public/image-comparison/quick-check
```

**Parameters:**
- `image1Url` (required): URL of the first image
- `image2Url` (required): URL of the second image
- `threshold` (optional): Similarity threshold (default: 0.95)

**Example:**
```
GET /api/public/image-comparison/quick-check?image1Url=https://example.com/image1.jpg&image2Url=https://example.com/image2.jpg&threshold=0.90
```

**Response:**
```json
{
  "areSimilar": false,
  "similarityScore": 0.87,
  "similarityPercentage": "87.00%",
  "threshold": 0.90,
  "assessment": "Images are similar with noticeable differences"
}
```

### 3. Get Difference Map

```
GET /api/public/image-comparison/difference-map
```

**Parameters:**
- `image1` (required): URL of the first image
- `image2` (required): URL of the second image
- `format` (optional): Output format - png, jpg, jpeg (default: png)

**Example:**
```
GET /api/public/image-comparison/difference-map?image1=https://example.com/image1.jpg&image2=https://example.com/image2.jpg&format=png
```

**Response:** Binary image data showing highlighted differences

### 4. Supported Formats

```
GET /api/public/image-comparison/supported-formats
```

**Response:**
```json
{
  "inputFormats": ["jpg", "jpeg", "png", "gif", "bmp", "webp"],
  "outputFormats": ["png", "jpg", "jpeg"],
  "maxImageSize": "10MB",
  "recommendedSize": "Under 2MB for best performance"
}
```

## Similarity Metrics Explained

### Overall Similarity (0.0 - 1.0)
Weighted combination of all metrics:
- Pixel Similarity: 30%
- Structural Similarity: 30% 
- Histogram Similarity: 20%
- Perceptual Hash: 20%

### Thresholds
- **≥ 0.98**: Virtually identical
- **≥ 0.95**: Very similar with minor differences
- **≥ 0.85**: Similar with noticeable differences  
- **≥ 0.70**: Significant differences
- **≥ 0.50**: Quite different
- **< 0.50**: Very different

### Individual Metrics

**Pixel Similarity**: Direct pixel-by-pixel comparison with color distance tolerance

**Structural Similarity**: Simplified SSIM algorithm focusing on luminance, contrast, and structure

**Histogram Similarity**: Compares color distribution patterns using correlation

**Perceptual Hash**: Compares structural patterns using simplified pHash algorithm

## Use Cases

### Website Monitoring
```bash
# Check if a webpage has changed
curl "http://localhost:8080/api/public/image-comparison/quick-check?image1Url=https://example.com/screenshot-old.png&image2Url=https://example.com/screenshot-new.png&threshold=0.95"
```

### Product Image Comparison
```bash
# Compare product images for changes
curl "http://localhost:8080/api/public/image-comparison/compare?image1Url=https://store.com/product-before.jpg&image2Url=https://store.com/product-after.jpg&includeDifferenceMap=true"
```

### Quality Assurance
```bash
# Verify UI consistency across deployments
curl "http://localhost:8080/api/public/image-comparison/compare?image1Url=https://staging.app.com/page.png&image2Url=https://prod.app.com/page.png"
```

## Performance Notes

- Images are automatically resized to optimal comparison dimensions (max 1000x1000)
- Minimum size enforced (200x200) for meaningful comparison
- Best performance with images under 2MB
- Supports concurrent comparisons
- Results are not cached (implement caching if needed for your use case)

## Error Handling

The API returns appropriate HTTP status codes:
- `200`: Successful comparison
- `400`: Invalid image URLs or unsupported format
- `404`: Difference map not found
- `500`: Internal server error

Error responses include descriptive messages:
```json
{
  "error": "Error processing images: Unable to load image from URL"
}
```
