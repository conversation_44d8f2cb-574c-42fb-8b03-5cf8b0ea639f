# Screenshot API Documentation

## Overview
The Screenshot API allows you to capture screenshots of supported e-commerce websites using the scraper factory pattern. The API validates that the URL is supported by checking if a scraper exists for the domain, then captures a full-page screenshot using ScrapingBee.

## Endpoint

### Capture Screenshot
**GET** `/api/public/screenshot`

Captures a screenshot of the provided URL and returns the screenshot URL.

#### Parameters
- `url` (required): The URL to capture a screenshot of

#### Supported Domains
- costco.ca
- costco.com  
- wayfair.ca
- wayfair.com
- walmart.com

#### Response Format
```json
{
  "screenshotUrl": "/screenshots/screenshot_20241213_143022_a1b2c3d4.jpg",
  "originalUrl": "https://www.costco.ca/some-product.html",
  "capturedAt": "2024-12-13T14:30:22",
  "status": "success",
  "message": "Screenshot captured successfully"
}
```

#### Error Response
```json
{
  "screenshotUrl": null,
  "originalUrl": "https://unsupported-site.com",
  "capturedAt": "2024-12-13T14:30:22",
  "status": "error",
  "message": "Unsupported URL: https://unsupported-site.com"
}
```

## Usage Examples

### cURL
```bash
# Capture screenshot of a Costco product
curl "http://localhost:8080/api/public/screenshot?url=https://www.costco.ca/kirkland-signature-organic-extra-virgin-olive-oil%2c-2-l.product.100334877.html"

# Capture screenshot of a Walmart product  
curl "http://localhost:8080/api/public/screenshot?url=https://www.walmart.com/ip/Great-Value-Whole-Vitamin-D-Milk-Gallon-128-fl-oz/10450114"
```

### JavaScript/Fetch
```javascript
const captureScreenshot = async (url) => {
  try {
    const response = await fetch(`/api/public/screenshot?url=${encodeURIComponent(url)}`);
    const data = await response.json();
    
    if (data.status === 'success') {
      console.log('Screenshot URL:', data.screenshotUrl);
      return data.screenshotUrl;
    } else {
      console.error('Error:', data.message);
      return null;
    }
  } catch (error) {
    console.error('Request failed:', error);
    return null;
  }
};

// Usage
const screenshotUrl = await captureScreenshot('https://www.costco.ca/some-product.html');
```

## Implementation Details

### File Storage
- Screenshots are saved locally in the `screenshots/` directory
- Filenames follow the pattern: `screenshot_YYYYMMDD_HHMMSS_UNIQUEID.jpg`
- Files are served as static resources via `/screenshots/` URL path

### ScrapingBee Configuration
- Uses full-page screenshot capture (`screenshot_full_page=true`)
- Enables stealth proxy to avoid detection (`stealth_proxy=true`)
- Leverages existing ScrapingBee API key configuration

### Error Handling
- **400 Bad Request**: Unsupported URL (no scraper available for domain)
- **500 Internal Server Error**: Screenshot capture failed (network issues, ScrapingBee errors)

### Factory Integration
The API uses the existing `ScraperFactory` to validate supported URLs:
- Calls `scraperFactory.getScraper(url)` to check if URL is supported
- Throws `IllegalArgumentException` for unsupported domains
- No actual scraping is performed - only screenshot capture

## Future Enhancements
- Upload screenshots to cloud storage (S3, Supabase Storage) for better scalability
- Add authentication for private screenshot endpoints
- Implement screenshot caching to avoid duplicate captures
- Add image optimization and compression
- Support custom screenshot dimensions and formats
