-- Create bucket for items (screenshots)
INSERT INTO storage.buckets (id, name, public)
VALUES ('items', 'items', true);

-- Enable RLS
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Policy for uploading items
CREATE POLICY "Users can upload their own items"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'items' AND
  (storage.foldername(name))[1] = 'screenshots'
);

-- Policy for viewing items (public read for screenshots)
CREATE POLICY "Anyone can view items"
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'items');

-- Policy for deleting items
CREATE POLICY "Users can delete their own items"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'items' AND
  (storage.foldername(name))[1] = 'screenshots'
);
