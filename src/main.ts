import './app.css';
import App from './App.svelte';
import { checkRequiredEnvVars } from './lib/utils/env';
import { initializeAnalytics } from './lib/utils/analytics';
import Clarity from '@microsoft/clarity';

// Check environment variables before mounting
checkRequiredEnvVars();
initializeAnalytics();
const projectId = "rvvjhwbp51"
Clarity.init(projectId);

const target = document.getElementById('app');
if (!target) {
  throw new Error('No #app element found in index.html');
}

const app = new App({
  target,
});

export default app;