import type { ItemDetail } from './itemDetail';

export interface UploadMetadata {
  id: string;
  userId: string;
  userEmail: string;
  filePath: string;
  fileUrl: string;
  fileType: string;
  uploadTime: string;
  status: string;
}

export interface ReceiptData {
  id: string;
  receiptId: string;
  barcodeNumber: string;
  memberNumber: string;
  storeAddress: string;
  province: string;
  items: Record<string, ItemDetail>;
  processedTime: string;
}

export interface ItemDetail {
  itemNumber: string;
  itemName: string;
  discountApplied: boolean;
  priceBeforeDiscount: number;
  priceAfterDiscount: number;
  discount: number;
}