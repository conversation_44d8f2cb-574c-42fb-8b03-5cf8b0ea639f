export interface FlyerItem {
    productName: string;
    itemNumber: string;
    itemUrl: string;
    imageUrl: string;
    warehouseOnly: boolean;
    warehousePrice: string;
    ecoFee: string | null;
    instantSavings: string;
    price: string;
    validFrom: string;
    validTo: string;
    online: boolean;
  }
  
  export interface Flyer {
    flyer: string;
    vendor: string;
    createdTimestamp: string;
    lastUpdatedTimestamp: string;
    validFrom: string;
    validTo: string;
    items: FlyerItem[];
  }