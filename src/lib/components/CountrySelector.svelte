<script lang="ts">
  import { onMount } from 'svelte';
  import { ChevronDown } from 'lucide-svelte';
  import { getUserProfile, updateUserCountry, type UserProfile } from '../api/community';
  import { user } from '../stores/auth';
  import { selectedCountry as selectedCountryStore } from '../stores/userPreferences';

  export let theme: 'light' | 'dark' = 'light';

  let isOpen = false;
  let selectedCountry = 'CA'; // Default to Canada
  let userProfile: UserProfile | null = null;
  let isLoading = false;
  let hasProfile = false;

  // Subscribe to the store for users without profiles
  $: if (!hasProfile) {
    selectedCountry = $selectedCountryStore;
  }

  const countries = [
    { code: 'CA', name: 'Canada', flag: '🇨🇦' },
    { code: 'US', name: 'United States', flag: '🇺🇸' }
  ];

  onMount(async () => {
    if ($user) {
      await loadUserProfile();
    }
  });

  async function loadUserProfile() {
    console.log('🔍 CountrySelector: Loading user profile...');
    console.log('🔍 CountrySelector: Current user from store:', $user);
    try {
      userProfile = await getUserProfile();
      console.log('🔍 CountrySelector: User profile result:', userProfile);
      if (userProfile) {
        hasProfile = true;
        console.log('✅ CountrySelector: User has profile, country:', userProfile.country);
        if (userProfile.country) {
          selectedCountry = userProfile.country;
        }
      } else {
        hasProfile = false;
        console.log('❌ CountrySelector: User has no profile');
      }
    } catch (error) {
      console.error('❌ CountrySelector: Error loading user profile:', error);
      hasProfile = false;
      // Keep default country if profile doesn't exist yet
    }
    console.log('🔍 CountrySelector: Final state - hasProfile:', hasProfile, 'selectedCountry:', selectedCountry);
  }

  async function handleCountryChange(countryCode: string) {
    console.log('🔄 CountrySelector: Attempting to change country to:', countryCode);
    console.log('🔍 CountrySelector: Current state - selectedCountry:', selectedCountry, 'hasProfile:', hasProfile, 'isLoading:', isLoading);

    if (countryCode === selectedCountry || isLoading) {
      console.log('⏭️ CountrySelector: Skipping - same country or loading');
      return;
    }

    // If user doesn't have a profile yet, update the store
    if (!hasProfile) {
      console.log('📝 CountrySelector: No profile - updating store only');
      selectedCountryStore.set(countryCode);
      selectedCountry = countryCode;
      isOpen = false;
      console.log('✅ CountrySelector: Store updated, selectedCountry now:', selectedCountry);
      return;
    }

    console.log('🌐 CountrySelector: User has profile - making API call');
    isLoading = true;
    try {
      console.log('📡 CountrySelector: Calling updateUserCountry API with:', countryCode);
      await updateUserCountry(countryCode);
      console.log('✅ CountrySelector: API call successful');
      selectedCountry = countryCode;
      // Also update the store for consistency
      selectedCountryStore.set(countryCode);
      isOpen = false;
      console.log('✅ CountrySelector: Country updated to:', selectedCountry);
    } catch (error: any) {
      console.error('❌ CountrySelector: Error updating country:', error);
      console.error('❌ CountrySelector: Error details:', {
        message: error.message,
        status: error.status,
        response: error.response
      });
      // Could show a toast notification here
    } finally {
      isLoading = false;
    }
  }

  function handleClickOutside(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target.closest('.country-selector')) {
      isOpen = false;
    }
  }

  $: selectedCountryData = countries.find(c => c.code === selectedCountry) || countries[0];
</script>

<svelte:window on:click={handleClickOutside} />

{#if $user}
  <div class="relative country-selector">
    <button
      class="flex items-center justify-between w-full px-3 py-2 text-sm rounded-lg transition-all duration-200 border focus:outline-none focus:ring-2 focus:ring-primary/50
             {theme === 'dark'
               ? 'text-gray-300 hover:text-white hover:bg-dark-lighter border-gray-700/50 hover:border-gray-600/50'
               : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50 border-gray-200 hover:border-gray-300'}"
      on:click={() => isOpen = !isOpen}
      disabled={isLoading}
      aria-label="Select country"
    >
      <div class="flex items-center space-x-2">
        <span class="text-lg">{selectedCountryData.flag}</span>
        <span class="font-medium">{selectedCountryData.code}</span>
      </div>
      <ChevronDown class="h-4 w-4 transition-transform duration-200 {isOpen ? 'rotate-180' : ''}" />
    </button>

    {#if isOpen}
      <div class="absolute right-0 mt-2 w-48 rounded-lg shadow-lg border z-50 overflow-hidden
                  {theme === 'dark'
                    ? 'bg-dark border-gray-700/50 backdrop-blur-xl'
                    : 'bg-white border-gray-200'}"
      >
        <div class="py-1">
          {#each countries as country}
            <button
              class="w-full flex items-center space-x-3 px-4 py-3 text-sm text-left transition-colors duration-200
                     {selectedCountry === country.code
                       ? 'bg-primary/20 text-primary border-l-2 border-primary'
                       : theme === 'dark'
                         ? 'text-gray-300 hover:text-white hover:bg-dark-lighter'
                         : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'}"
              on:click={() => handleCountryChange(country.code)}
              disabled={isLoading}
            >
              <span class="text-lg">{country.flag}</span>
              <div class="flex-1">
                <div class="font-medium">{country.name}</div>
                <div class="text-xs {theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}">{country.code}</div>
              </div>
              {#if selectedCountry === country.code}
                <div class="w-2 h-2 bg-primary rounded-full"></div>
              {/if}
            </button>
          {/each}
        </div>
        
        {#if isLoading}
          <div class="px-4 py-2 border-t {theme === 'dark' ? 'border-gray-700/50' : 'border-gray-200'}">
            <div class="flex items-center space-x-2 text-xs {theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}">
              <div class="w-3 h-3 border border-primary border-t-transparent rounded-full animate-spin"></div>
              <span>Updating...</span>
            </div>
          </div>
        {/if}
      </div>
    {/if}
  </div>
{/if}

<style>
  .country-selector {
    user-select: none;
  }
</style>
