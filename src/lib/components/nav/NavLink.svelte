<script lang="ts">
    export let href: string;
    export let active = false;

    import { page } from '../../stores/navigation';

    function handleClick(event: MouseEvent) {
      event.preventDefault();
      page.set(href);
    }

    // Convert internal routes to proper URLs for SEO
    $: fullHref = href === 'home' ? '/' : `/${href}`;
  </script>

  <!-- Use proper <a> tag with href for SEO, but intercept clicks for SPA navigation -->
  <a
    href={fullHref}
    on:click={handleClick}
    class="px-3 py-2 text-sm text-gray-300 hover:text-white transition-colors rounded-lg inline-block
           {active ? 'bg-dark-lighter text-white' : 'hover:bg-dark-lighter/50'}"
  >
    <slot />
  </a>