<script lang="ts">
  import profileImage from '../assets/profile.jpg';
  import ContextualLinks from './seo/ContextualLinks.svelte';
</script>

<svelte:head>
  <title>About BargainHawk | Costco Price Tracking Tool</title>
  <meta name="description" content="Learn about BargainHawk - your automated Costco price tracking solution. Save money with automatic price drop notifications and easy price adjustment claims." />
  <meta name="keywords" content="costco price tracking, costco price monitoring, costco price alerts, costco.ca price tracker" />
  <link rel="canonical" href="https://bargainhawk.ca/about" />
</svelte:head>

<div class="min-h-screen bg-gray-50 pt-20">
  <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="text-center">
      <div class="w-64 h-64 rounded-full bg-gray-200 mx-auto mb-8 overflow-hidden">
        <img
          src={profileImage}
          alt="Founder"
          class="w-full h-full object-cover"
          onError={(e) => {
            e.currentTarget.style.display = 'none';
            e.currentTarget.parentElement.innerHTML = '<span class="text-gray-400 flex items-center justify-center h-full">Photo</span>';
          }}
        />
      </div>
      
      <h1 class="text-4xl font-bold text-gray-900 mb-6">About BargainHawk</h1>
      
      <div class="prose prose-lg mx-auto text-gray-500">
        <p class="mb-4">
          Hi, I'm a solo founder who noticed a common problem among online shoppers: the constant need to manually check prices for desired items, especially on Costco's website.
        </p>
        
        <p class="mb-4">
          As someone who frequently shops online, I found myself repeatedly visiting the same product pages, hoping to catch price drops. This manual process was time-consuming and inefficient.
        </p>
        
        <p class="mb-4">
          That's why I created BargainHawk - a simple yet powerful tool that automatically tracks prices for you. Currently focusing on Costco during our pilot phase, BargainHawk monitors your selected products and notifies you when prices match your target.
        </p>
        
        <p class="mb-4">
          My goal is to help fellow shoppers save both time and money, making online shopping more efficient and rewarding. As we grow, we plan to expand our service to include more retailers and features based on user feedback.
        </p>

        <p class="text-sm text-gray-400 mt-8">
          For any inquiries, please contact: <a href="mailto:<EMAIL>" class="text-primary hover:text-secondary"><EMAIL></a>
        </p>
      </div>

      <!-- Contextual links for SEO -->
      <div class="mt-12 bg-white rounded-lg p-8 shadow-sm">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Ready to Start Saving?</h2>
        <div class="grid md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Get Started</h3>
            <ul class="space-y-2">
              <li><a href="/auth" class="text-primary hover:text-primary-dark underline">Create Free Account</a></li>
              <li><a href="/feed" class="text-primary hover:text-primary-dark underline">Browse Current Price Drops</a></li>
              <li><a href="/faq" class="text-primary hover:text-primary-dark underline">Frequently Asked Questions</a></li>
            </ul>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Learn More</h3>
            <ul class="space-y-2">
              <li><a href="/blog/costco-price-adjustment-hack" class="text-primary hover:text-primary-dark underline">Costco Price Adjustment Guide</a></li>
              <li><a href="/blog/walmart-price-adjustment-guide" class="text-primary hover:text-primary-dark underline">Walmart Price Adjustments</a></li>
              <li><a href="/blog" class="text-primary hover:text-primary-dark underline">All Money-Saving Articles</a></li>
              <li><a href="/community" class="text-primary hover:text-primary-dark underline">Join Our Community</a></li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Contextual links for SEO and user engagement -->
      <ContextualLinks pageType="about" />
    </div>
  </div>
</div>