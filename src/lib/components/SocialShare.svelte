<script lang="ts">
  import { Share2, Twitter, Facebook, MessageCircle, Copy, Check } from 'lucide-svelte';
  
  export let title: string = '';
  export let description: string = '';
  export let url: string = '';
  export let hashtags: string[] = [];
  export let savings: string = '';
  export let compact: boolean = false;
  
  let showShareMenu = false;
  let copySuccess = false;
  
  $: shareUrl = url || (typeof window !== 'undefined' ? window.location.href : '');
  $: shareTitle = title || 'Check out this amazing deal I found on BargainHawk!';
  $: shareDescription = description || `I just saved ${savings || 'money'} using BargainHawk's price tracking. Never pay full price again!`;
  $: shareHashtags = hashtags.length > 0 ? hashtags : ['BargainHawk', 'DealsCanada', 'MoneySaving', 'PriceTracking'];
  
  function toggleShareMenu() {
    showShareMenu = !showShareMenu;
  }
  
  function shareOnTwitter() {
    const text = `${shareTitle} ${shareDescription}`;
    const hashtagString = shareHashtags.map(tag => `#${tag}`).join(' ');
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(shareUrl)}&hashtags=${encodeURIComponent(shareHashtags.join(','))}`;
    window.open(twitterUrl, '_blank', 'width=550,height=420');
    showShareMenu = false;
  }
  
  function shareOnFacebook() {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(shareTitle + ' - ' + shareDescription)}`;
    window.open(facebookUrl, '_blank', 'width=550,height=420');
    showShareMenu = false;
  }
  
  function shareOnWhatsApp() {
    const text = `${shareTitle}\n\n${shareDescription}\n\n${shareUrl}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(text)}`;
    window.open(whatsappUrl, '_blank');
    showShareMenu = false;
  }
  
  async function copyToClipboard() {
    try {
      const textToCopy = `${shareTitle}\n\n${shareDescription}\n\n${shareUrl}`;
      await navigator.clipboard.writeText(textToCopy);
      copySuccess = true;
      setTimeout(() => {
        copySuccess = false;
        showShareMenu = false;
      }, 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = `${shareTitle}\n\n${shareDescription}\n\n${shareUrl}`;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      copySuccess = true;
      setTimeout(() => {
        copySuccess = false;
        showShareMenu = false;
      }, 2000);
    }
  }
  
  function handleClickOutside(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target.closest('.share-container')) {
      showShareMenu = false;
    }
  }
</script>

<svelte:window on:click={handleClickOutside} />

<div class="share-container relative">
  {#if compact}
    <!-- Compact Share Button -->
    <button
      class="flex items-center gap-1 px-2 py-1 text-gray-400 hover:text-primary transition-colors text-sm"
      on:click|stopPropagation={toggleShareMenu}
      aria-label="Share"
    >
      <Share2 class="h-4 w-4" />
      <span>Share</span>
    </button>
  {:else}
    <!-- Full Share Button -->
    <button
      class="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors"
      on:click|stopPropagation={toggleShareMenu}
    >
      <Share2 class="h-4 w-4" />
      <span>Share Your Savings</span>
    </button>
  {/if}
  
  <!-- Share Menu -->
  {#if showShareMenu}
    <div class="absolute {compact ? 'top-full right-0' : 'top-full left-0'} mt-2 bg-dark-lighter border border-gray-700 rounded-lg shadow-xl z-50 min-w-48">
      <div class="p-2">
        <div class="text-xs text-gray-400 mb-2 px-2">Share your savings success!</div>
        
        <!-- Twitter -->
        <button
          class="w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-dark rounded-lg transition-colors text-sm"
          on:click={shareOnTwitter}
        >
          <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <Twitter class="h-4 w-4 text-white" />
          </div>
          <div>
            <div class="text-white font-medium">Twitter</div>
            <div class="text-xs text-gray-400">Share with your followers</div>
          </div>
        </button>
        
        <!-- Facebook -->
        <button
          class="w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-dark rounded-lg transition-colors text-sm"
          on:click={shareOnFacebook}
        >
          <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <Facebook class="h-4 w-4 text-white" />
          </div>
          <div>
            <div class="text-white font-medium">Facebook</div>
            <div class="text-xs text-gray-400">Share with friends</div>
          </div>
        </button>
        
        <!-- WhatsApp -->
        <button
          class="w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-dark rounded-lg transition-colors text-sm"
          on:click={shareOnWhatsApp}
        >
          <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <MessageCircle class="h-4 w-4 text-white" />
          </div>
          <div>
            <div class="text-white font-medium">WhatsApp</div>
            <div class="text-xs text-gray-400">Send to contacts</div>
          </div>
        </button>
        
        <!-- Copy Link -->
        <button
          class="w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-dark rounded-lg transition-colors text-sm"
          on:click={copyToClipboard}
        >
          <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
            {#if copySuccess}
              <Check class="h-4 w-4 text-green-400" />
            {:else}
              <Copy class="h-4 w-4 text-white" />
            {/if}
          </div>
          <div>
            <div class="text-white font-medium">
              {copySuccess ? 'Copied!' : 'Copy Link'}
            </div>
            <div class="text-xs text-gray-400">
              {copySuccess ? 'Link copied to clipboard' : 'Copy to clipboard'}
            </div>
          </div>
        </button>
      </div>
      
      <!-- Share Stats -->
      <div class="border-t border-gray-700 p-3">
        <div class="text-xs text-gray-400 text-center">
          💡 <strong>Pro tip:</strong> Friends who join through your shares get bonus alerts!
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .share-container {
    position: relative;
  }
</style>
