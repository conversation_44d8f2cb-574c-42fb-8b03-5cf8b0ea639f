<script lang="ts">
  import { onMount } from 'svelte';
  import SearchBar from './SearchBar.svelte';
  import SupportedStores from './SupportedStores.svelte';

  const phrases = ['Track Costco Prices', 'Get Price Drop Alerts', 'Claim Price Adjustments'];
  let currentText = '';
  let phraseIndex = 0;
  let isDeleting = false;
  let typingSpeed = 100;
  let deletingSpeed = 50;

  function typeText() {
    const currentPhrase = phrases[phraseIndex];
    let nextTimeout = isDeleting ? deletingSpeed : typingSpeed;

    if (isDeleting) {
      currentText = currentPhrase.substring(0, currentText.length - 1);
      if (currentText === '') {
        isDeleting = false;
        phraseIndex = (phraseIndex + 1) % phrases.length;
        nextTimeout = 500; // Pause before typing next phrase
      }
    } else {
      currentText = currentPhrase.substring(0, currentText.length + 1);
      if (currentText === currentPhrase) {
        isDeleting = true;
        nextTimeout = 2000; // Pause before deleting
      }
    }

    const timeoutId = window.setTimeout(typeText, nextTimeout);
    return () => window.clearTimeout(timeoutId);
  }

  onMount(() => {
    const cleanup = typeText();
    return cleanup;
  });
</script>

<section aria-label="Hero Section" class="text-center">
  <h1 class="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl">
    <span class="block text-blue-200 h-[1.5em] flex items-center justify-center">
      {currentText}<span class="animate-pulse ml-0.5" aria-hidden="true">|</span>
    </span>
  </h1>
  <p class="mt-3 max-w-md mx-auto text-base text-blue-100 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
    Never miss a Costco price drop again! Track prices automatically and get notified when your items go on sale. Perfect for price adjustments and savings.
  </p>
  <ul class="mt-6 text-sm text-blue-200 space-y-2 max-w-md mx-auto" role="list">
    <li>✓ Automatic price tracking for Costco</li>
    <li>✓ Email notifications for price drops</li>
    <li>✓ Easy price adjustment claims</li>
  </ul>
  <div class="mt-10 flex flex-col items-center">
    <SearchBar />
    <SupportedStores />
  </div>
</section>