<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { fade, scale } from 'svelte/transition';
  import { X, Monitor, DollarSign } from 'lucide-svelte';

  export let show = false;

  const dispatch = createEventDispatcher();

  function handleClose() {
    dispatch('close');
  }

  function handleBackdropClick(event: MouseEvent) {
    if (event.target === event.currentTarget) {
      handleClose();
    }
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      handleClose();
    }
  }
</script>

{#if show}
  <div
    class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50"
    on:click={handleBackdropClick}
    on:keydown={handleKeydown}
    transition:fade={{ duration: 200 }}
    role="dialog"
    aria-modal="true"
    aria-labelledby="website-tracking-limit-title"
    tabindex="-1"
  >
    <div
      class="bg-dark-lighter border border-gray-800 rounded-lg shadow-xl max-w-md w-full p-6"
      transition:scale={{ duration: 200, start: 0.95 }}
    >
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-primary/10 rounded-lg">
            <Monitor class="w-6 h-6 text-primary" />
          </div>
          <h2 id="website-tracking-limit-title" class="text-xl font-semibold text-white">
            Website Tracking Limit Reached
          </h2>
        </div>
        <button
          on:click={handleClose}
          class="text-gray-400 hover:text-white transition-colors p-1"
          aria-label="Close modal"
        >
          <X class="w-5 h-5" />
        </button>
      </div>

      <!-- Content -->
      <div class="space-y-4 mb-6">
        <div class="flex items-start space-x-3">
          <div class="p-2 bg-green-500/10 rounded-lg flex-shrink-0 mt-1">
            <DollarSign class="w-5 h-5 text-green-400" />
          </div>
          <div>
            <h3 class="text-white font-medium mb-2">
              Thank you for using BargainHawk! 🎉
            </h3>
            <p class="text-gray-300 text-sm leading-relaxed">
              To keep our servers running and maintain our free service for everyone, 
              we currently limit each account to <strong class="text-white">4 website tracking items</strong>.
            </p>
          </div>
        </div>

        <div class="bg-dark/50 border border-gray-800/50 rounded-lg p-4">
          <h4 class="text-white font-medium mb-2">What you can do:</h4>
          <ul class="text-gray-300 text-sm space-y-2">
            <li class="flex items-start">
              <span class="text-primary mr-2">•</span>
              Remove a website you no longer need to track
            </li>
            <li class="flex items-start">
              <span class="text-primary mr-2">•</span>
              Focus on your most important websites for maximum savings
            </li>
            <li class="flex items-start">
              <span class="text-primary mr-2">•</span>
              Check back regularly as we may increase limits in the future
            </li>
          </ul>
        </div>

        <div class="text-center">
          <p class="text-gray-400 text-xs">
            Your support helps us keep BargainHawk free for all shoppers!
          </p>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex justify-end">
        <button
          on:click={handleClose}
          class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark 
                 transition-all duration-200 font-medium"
        >
          Got it, thanks!
        </button>
      </div>
    </div>
  </div>
{/if}
