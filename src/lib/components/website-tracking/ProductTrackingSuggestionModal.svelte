<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Modal from '../ui/Modal.svelte';
  import Button from '../ui/Button.svelte';
  import { ShoppingCart, Monitor, TrendingDown, AlertCircle } from 'lucide-svelte';
  
  export let show = false;
  export let url = '';
  
  const dispatch = createEventDispatcher();
  
  // Extract domain from URL for display
  $: domain = (() => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch {
      return '';
    }
  })();
  
  // Get store-specific messaging
  $: storeInfo = (() => {
    const lowerUrl = url.toLowerCase();
    if (lowerUrl.includes('costco.ca') || lowerUrl.includes('costco.com')) {
      return {
        name: 'Costco',
        icon: '🏪',
        benefits: ['Bulk pricing alerts', 'Member-exclusive deals', 'Seasonal price tracking']
      };
    } else if (lowerUrl.includes('walmart.com')) {
      return {
        name: 'Walmart',
        icon: '🛒',
        benefits: ['Rollback notifications', 'Clearance alerts', 'Price match tracking']
      };
    } else if (lowerUrl.includes('wayfair.ca') || lowerUrl.includes('wayfair.com')) {
      return {
        name: 'Wayfair',
        icon: '🏠',
        benefits: ['Furniture sale alerts', 'Seasonal discounts', 'Free shipping thresholds']
      };
    }
    return {
      name: 'this store',
      icon: '🛍️',
      benefits: ['Price drop alerts', 'Sale notifications', 'Target price tracking']
    };
  })();
  
  function handleUseProductTracking() {
    dispatch('useProductTracking', { url });
  }
  
  function handleContinueWebsiteTracking() {
    dispatch('continueWebsiteTracking', { url });
  }
  
  function handleClose() {
    dispatch('close');
  }
</script>

<Modal {show} on:click={handleClose} maxWidth="max-w-lg">
  <div class="p-6">
    <!-- Header -->
    <div class="flex items-center space-x-3 mb-4">
      <div class="p-2 bg-primary/20 rounded-lg">
        <TrendingDown class="h-6 w-6 text-primary" />
      </div>
      <div>
        <h2 class="text-xl font-bold text-white">Better Price Tracking Available!</h2>
        <p class="text-sm text-gray-400">{domain}</p>
      </div>
    </div>
    
    <!-- Alert Message -->
    <div class="bg-amber-500/10 border border-amber-500/20 rounded-lg p-4 mb-6">
      <div class="flex items-start space-x-3">
        <AlertCircle class="h-5 w-5 text-amber-400 mt-0.5 flex-shrink-0" />
        <div>
          <p class="text-amber-200 text-sm font-medium mb-1">
            We detected you're trying to track {storeInfo.name} {storeInfo.icon}
          </p>
          <p class="text-amber-300/80 text-xs">
            For more accurate price drop detection, we recommend using our dedicated product tracking feature instead.
          </p>
        </div>
      </div>
    </div>
    
    <!-- Comparison -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      <!-- Product Tracking (Recommended) -->
      <div class="bg-primary/10 border border-primary/30 rounded-lg p-4">
        <div class="flex items-center space-x-2 mb-3">
          <ShoppingCart class="h-5 w-5 text-primary" />
          <h3 class="font-semibold text-white text-sm">Product Tracking</h3>
          <span class="bg-primary text-white text-xs px-2 py-0.5 rounded-full">Recommended</span>
        </div>
        <ul class="space-y-2 text-xs text-gray-300">
          {#each storeInfo.benefits as benefit}
            <li class="flex items-center space-x-2">
              <div class="w-1.5 h-1.5 bg-primary rounded-full flex-shrink-0"></div>
              <span>{benefit}</span>
            </li>
          {/each}
          <li class="flex items-center space-x-2">
            <div class="w-1.5 h-1.5 bg-primary rounded-full flex-shrink-0"></div>
            <span>Email notifications</span>
          </li>
          <li class="flex items-center space-x-2">
            <div class="w-1.5 h-1.5 bg-primary rounded-full flex-shrink-0"></div>
            <span>Price history charts</span>
          </li>
        </ul>
      </div>
      
      <!-- Website Tracking -->
      <div class="bg-gray-800/30 border border-gray-700/50 rounded-lg p-4">
        <div class="flex items-center space-x-2 mb-3">
          <Monitor class="h-5 w-5 text-gray-400" />
          <h3 class="font-semibold text-gray-300 text-sm">Website Tracking</h3>
        </div>
        <ul class="space-y-2 text-xs text-gray-400">
          <li class="flex items-center space-x-2">
            <div class="w-1.5 h-1.5 bg-gray-500 rounded-full flex-shrink-0"></div>
            <span>Visual change detection</span>
          </li>
          <li class="flex items-center space-x-2">
            <div class="w-1.5 h-1.5 bg-gray-500 rounded-full flex-shrink-0"></div>
            <span>Screenshot comparisons</span>
          </li>
          <li class="flex items-center space-x-2">
            <div class="w-1.5 h-1.5 bg-gray-500 rounded-full flex-shrink-0"></div>
            <span>Manual area selection</span>
          </li>
          <li class="flex items-center space-x-2">
            <div class="w-1.5 h-1.5 bg-gray-500 rounded-full flex-shrink-0"></div>
            <span>Less precise for prices</span>
          </li>
        </ul>
      </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
      <Button
        on:click={handleUseProductTracking}
        customClass="flex-1 bg-primary hover:bg-primary-dark text-white font-medium"
      >
        Use Product Tracking
      </Button>

      <Button
        on:click={handleContinueWebsiteTracking}
        variant="secondary"
        customClass="flex-1"
      >
        Continue with Website Tracking
      </Button>
    </div>
    
    <!-- Footer Note -->
    <p class="text-xs text-gray-500 text-center mt-4">
      You can always switch between tracking methods later in your dashboard.
    </p>
  </div>
</Modal>
