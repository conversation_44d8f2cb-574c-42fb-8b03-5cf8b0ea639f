<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { user, signOut } from '../stores/auth';
  import { page } from '../stores/navigation';
  import UserAvatar from './UserAvatar.svelte';
  import CountrySelector from './CountrySelector.svelte';
  
  let isOpen = false;
  const dispatch = createEventDispatcher();

  function handleSignOut() {
    signOut();
    page.set('home');
    isOpen = false;
  }

  function handleClickOutside(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target.closest('.user-menu')) {
      isOpen = false;
    }
  }

  function goToDashboard() {
    page.set('dashboard');
    isOpen = false;
  }

  function goToPriceHistory() {
    page.set('price-history');
    isOpen = false;
  }

  function goToAccount() {
    page.set('account');
    isOpen = false;
  }
</script>

<svelte:window on:click={handleClickOutside} />

<div class="relative user-menu">
  <button
    class="focus:outline-none"
    on:click={() => isOpen = !isOpen}
    aria-label="User menu"
  >
    <UserAvatar />
  </button>

  {#if isOpen}
    <div class="absolute right-0 mt-2 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
      {#if $user?.email}
        <div class="px-4 py-3 border-b border-gray-100">
          <p class="text-sm text-gray-500">Signed in as:</p>
          <p class="text-sm font-medium text-gray-900 truncate">{$user.email}</p>
        </div>
      {/if}
      <div class="py-1" role="menu">
        <button
          class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          on:click={goToDashboard}
          role="menuitem"
        >
          Dashboard
        </button>
        <button
          class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          on:click={goToPriceHistory}
          role="menuitem"
        >
          Price History
        </button>
        <button
          class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          on:click={goToAccount}
          role="menuitem"
        >
          Account
        </button>
        <div class="px-4 py-2 border-t border-gray-100">
          <div class="text-xs text-gray-500 mb-2">Country/Region</div>
          <CountrySelector />
        </div>
        <button
          class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 border-t border-gray-100"
          on:click={handleSignOut}
          role="menuitem"
        >
          Sign out
        </button>
      </div>
    </div>
  {/if}
</div>