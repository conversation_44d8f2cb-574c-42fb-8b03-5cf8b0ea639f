<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import type { PriceHistoryItem } from '../../types/priceHistory';
    import { formatPrice, formatDate } from '../../utils/format';
    import Chart from 'chart.js/auto';
    
    export let data: PriceHistoryItem[] = [];
    
    let canvas: HTMLCanvasElement;
    let chart: Chart | null = null;
    let productImage = '';
    
    $: if (data.length > 0) {
      productImage = data[0].imageUrl;
    }
    
    $: sortedData = [...data].sort((a, b) => 
      new Date(a.dateTime).getTime() - new Date(b.dateTime).getTime()
    );
    
    $: if (canvas && sortedData.length > 0) {
      updateChart();
    }
  
    function createChart() {
      if (!canvas) return;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;
  
      chart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: sortedData.map(item => formatDate(item.dateTime)),
          datasets: [{
            label: 'Price',
            data: sortedData.map(item => item.price),
            borderColor: '#0EA5E9',
            backgroundColor: 'rgba(14, 165, 233, 0.1)',
            borderWidth: 2,
            pointBackgroundColor: '#0EA5E9',
            pointRadius: 4,
            pointHoverRadius: 6,
            fill: true,
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          interaction: {
            intersect: false,
            mode: 'index'
          },
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: (context) => formatPrice(context.parsed.y)
              },
              backgroundColor: 'rgba(15, 23, 42, 0.9)',
              titleColor: '#E2E8F0',
              bodyColor: '#F8FAFC',
              borderColor: '#1E293B',
              borderWidth: 1,
              padding: 12,
              bodyFont: {
                family: "'Inter var', sans-serif"
              }
            }
          },
          scales: {
            x: {
              grid: {
                color: 'rgba(148, 163, 184, 0.1)'
              },
              ticks: {
                color: '#94A3B8'
              }
            },
            y: {
              grid: {
                color: 'rgba(148, 163, 184, 0.1)'
              },
              ticks: {
                color: '#94A3B8',
                callback: (value) => formatPrice(value as number)
              }
            }
          }
        }
      });
    }
  
    function updateChart() {
      if (!chart) {
        createChart();
        return;
      }
  
      chart.data.labels = sortedData.map(item => formatDate(item.dateTime));
      chart.data.datasets[0].data = sortedData.map(item => item.price);
      chart.update();
    }
  
    onMount(() => {
      if (data.length > 0) {
        createChart();
      }
    });
  
    onDestroy(() => {
      if (chart) {
        chart.destroy();
      }
    });
  </script>
  
  <div class="bg-dark-lighter/50 p-6 rounded-lg border border-gray-800/50 backdrop-blur-sm">
    {#if data.length === 0}
      <div class="text-center py-12">
        <div class="text-gray-500 mb-4">
          <svg class="w-16 h-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-white mb-2">No Price History Found</h3>
        <p class="text-gray-400">
          We haven't tracked any price changes for this product yet. Check back later or try another product.
        </p>
      </div>
    {:else}
      <div class="mb-6 flex items-center gap-4">
        {#if productImage}
          <img
            src={productImage}
            alt="Product"
            class="w-24 h-24 object-contain rounded-lg bg-dark/50"
          />
        {/if}
        <div>
          <h2 class="text-xl font-semibold text-white">Price History</h2>
          <p class="text-sm text-gray-400">
            Tracking prices from {formatDate(sortedData[0].dateTime)} to {formatDate(sortedData[sortedData.length - 1].dateTime)}
          </p>
        </div>
      </div>
      
      <div class="relative w-full" style="height: 400px;">
        <canvas bind:this={canvas}></canvas>
      </div>
      
      <div class="mt-4 grid grid-cols-2 gap-4">
        <div class="bg-dark/30 p-4 rounded-lg border border-gray-800/50">
          <div class="text-sm text-gray-400">Lowest Price</div>
          <div class="text-xl font-semibold text-primary">
            {formatPrice(Math.min(...sortedData.map(d => d.price)))}
          </div>
        </div>
        <div class="bg-dark/30 p-4 rounded-lg border border-gray-800/50">
          <div class="text-sm text-gray-400">Highest Price</div>
          <div class="text-xl font-semibold text-primary">
            {formatPrice(Math.max(...sortedData.map(d => d.price)))}
          </div>
        </div>
      </div>
    {/if}
  </div>