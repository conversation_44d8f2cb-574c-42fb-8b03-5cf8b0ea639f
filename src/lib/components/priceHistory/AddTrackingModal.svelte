<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import Modal from '../ui/Modal.svelte';
    import Button from '../ui/Button.svelte';
    import Input from '../ui/Input.svelte';
    import { validateProductUrl } from '../../utils/validation';
    import { addPriceTracking } from '../../services/priceHistory';
    
    export let show = false;
    
    const dispatch = createEventDispatcher();
    let url = '';
    let error = '';
    let loading = false;
  
    async function handleSubmit() {
      error = validateProductUrl(url);
      if (error) return;
      
      try {
        loading = true;
        await addPriceTracking(url);
        dispatch('success');
        handleClose();
      } catch (e) {
        error = e instanceof Error ? e.message : 'Failed to add tracking';
      } finally {
        loading = false;
      }
    }
  
    function handleClose() {
      url = '';
      error = '';
      dispatch('close');
    }
  </script>
  
  <Modal {show} on:click={handleClose}>
    <div class="p-6">
      <h2 class="text-2xl font-bold text-white mb-6">Add Price Tracking</h2>
      
      <form on:submit|preventDefault={handleSubmit} class="space-y-4">
        <Input
          label="Product URL"
          bind:value={url}
          required
          placeholder="https://www.costco.ca/product-url"
        />
        
        {#if error}
          <p class="text-red-400 text-sm">{error}</p>
        {/if}
        
        <div class="flex justify-end gap-3 pt-2">
          <Button variant="secondary" on:click={handleClose}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? 'Adding...' : 'Start Tracking'}
          </Button>
        </div>
      </form>
    </div>
  </Modal>