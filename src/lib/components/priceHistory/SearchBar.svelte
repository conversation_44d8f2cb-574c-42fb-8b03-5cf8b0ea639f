<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import Button from '../ui/Button.svelte';
    import Input from '../ui/Input.svelte';
    import { validateProductUrl } from '../../utils/validation';
    
    export let loading = false;
    
    const dispatch = createEventDispatcher();
    let url = '';
    let error = '';
  
    function handleSubmit() {
      error = validateProductUrl(url);
      if (!error) {
        dispatch('search', url);
      }
    }
  </script>
  
  <form on:submit|preventDefault={handleSubmit} class="w-full max-w-2xl mx-auto mb-8">
    <div class="relative">
      <Input
        type="url"
        label="Product URL"
        bind:value={url}
        placeholder="Paste product URL to view price history..."
        required
        disabled={loading}
      />
      <div class="absolute right-2 top-[2.1rem]">
        <Button type="submit" disabled={loading}>
          {loading ? 'Loading...' : 'Analyze'}
        </Button>
      </div>
    </div>
    {#if error}
      <p class="mt-2 text-sm text-red-400">{error}</p>
    {/if}
  </form>