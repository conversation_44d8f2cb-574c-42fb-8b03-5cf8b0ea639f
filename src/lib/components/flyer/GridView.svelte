<script lang="ts">
    import type { FlyerItem } from '../../types/flyer';
    import { fade } from 'svelte/transition';
    import { Tag, Store } from 'lucide-svelte';
    
    export let items: FlyerItem[];
  </script>
  
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    {#each items as item}
      <div 
        class="bg-dark-lighter/50 rounded-lg border border-gray-800/50 backdrop-blur-sm overflow-hidden
               hover:border-gray-700 transition-all duration-300"
        in:fade={{ duration: 200 }}
      >
        <a href={item.itemUrl} target="_blank" rel="noopener noreferrer" class="block">
          <div class="relative aspect-square bg-dark/50">
            <img
              src={item.imageUrl}
              alt={item.productName}
              class="w-full h-full object-contain p-4"
              loading="lazy"
            />
            {#if item.warehouseOnly}
              <div class="absolute top-2 right-2 px-2 py-1 bg-primary/90 text-white text-xs rounded-full backdrop-blur-sm">
                <Store class="w-4 h-4 inline-block mr-1" />
                In-Store Only
              </div>
            {/if}
          </div>
          
          <div class="p-4">
            <h3 class="font-medium text-white mb-2 line-clamp-2" title={item.productName}>
              {item.productName}
            </h3>
            
            <div class="space-y-2">
              {#if item.warehousePrice}
                <div class="flex items-baseline justify-between">
                  <span class="text-sm text-gray-400">Regular Price:</span>
                  <span class="text-gray-500 line-through">${item.warehousePrice}</span>
                </div>
              {/if}
              
              {#if item.price}
                <div class="flex items-baseline justify-between">
                  <span class="text-sm text-gray-400">Sale Price:</span>
                  <span class="text-lg font-bold text-primary">${item.price}</span>
                </div>
              {/if}
              
              {#if parseFloat(item.instantSavings || '0') > 0}
                <div class="flex items-center gap-2 bg-green-900/20 border border-green-500/30 p-2 rounded-lg">
                  <Tag class="w-4 h-4 text-green-400" />
                  <span class="text-green-400 text-sm font-medium">
                    Save ${item.instantSavings}
                  </span>
                </div>
              {/if}
              
              <div class="text-xs text-gray-500 mt-2">
                Valid until {new Date(item.validTo).toLocaleDateString()}
              </div>
            </div>
          </div>
        </a>
      </div>
    {/each}
  </div>