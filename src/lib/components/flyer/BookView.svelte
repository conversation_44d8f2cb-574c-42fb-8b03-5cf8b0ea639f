<script lang="ts">
    import type { FlyerItem } from '../../types/flyer';
    import { ChevronLeft, ChevronRight } from 'lucide-svelte';
    
    export let items: FlyerItem[];
    
    // Number of items per page
    const ITEMS_PER_PAGE = 6;
    
    // Calculate total pages
    $: totalPages = Math.ceil(items.length / ITEMS_PER_PAGE);
    let currentPage = 0;
    
    // Get current page items
    $: currentItems = items.slice(
      currentPage * ITEMS_PER_PAGE,
      (currentPage + 1) * ITEMS_PER_PAGE
    );
    
    function nextPage() {
      if (currentPage < totalPages - 1) {
        currentPage++;
      }
    }
    
    function prevPage() {
      if (currentPage > 0) {
        currentPage--;
      }
    }
  </script>
  
  <div class="relative">
    <div class="perspective-1000">
      <div class="book-page transform-style-preserve-3d">
        <div class="grid grid-cols-2 gap-6 p-8 bg-dark-lighter/50 rounded-lg border border-gray-800/50 backdrop-blur-sm">
          {#each currentItems as item}
            <div class="bg-dark/30 rounded-lg p-4 transform hover:scale-105 transition-transform duration-200">
              <a href={item.itemUrl} target="_blank" rel="noopener noreferrer" class="block">
                <img
                  src={item.imageUrl}
                  alt={item.productName}
                  class="w-full aspect-square object-contain bg-dark/50 rounded-lg mb-4"
                  loading="lazy"
                />
                
                <h3 class="font-medium text-white mb-2 line-clamp-2">
                  {item.productName}
                </h3>
                
                {#if item.warehousePrice || item.price}
                  <div class="flex items-baseline justify-between mb-2">
                    {#if item.warehousePrice}
                      <span class="text-gray-500 line-through">${item.warehousePrice}</span>
                    {/if}
                    {#if item.price}
                      <span class="text-lg font-bold text-primary">${item.price}</span>
                    {/if}
                  </div>
                {/if}
                
                {#if parseFloat(item.instantSavings || '0') > 0}
                  <div class="text-green-400 text-sm font-medium">
                    Save ${item.instantSavings}
                  </div>
                {/if}
              </a>
            </div>
          {/each}
        </div>
      </div>
    </div>
    
    <div class="flex justify-between items-center mt-6">
      <button
        class="p-2 rounded-full bg-dark-lighter text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
        on:click={prevPage}
        disabled={currentPage === 0}
      >
        <ChevronLeft class="w-6 h-6" />
      </button>
      
      <div class="text-gray-400">
        Page {currentPage + 1} of {totalPages}
      </div>
      
      <button
        class="p-2 rounded-full bg-dark-lighter text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
        on:click={nextPage}
        disabled={currentPage === totalPages - 1}
      >
        <ChevronRight class="w-6 h-6" />
      </button>
    </div>
  </div>
  
  <style>
    .perspective-1000 {
      perspective: 1000px;
    }
    
    .transform-style-preserve-3d {
      transform-style: preserve-3d;
    }
    
    .book-page {
      transition: transform 0.5s;
    }
  </style>