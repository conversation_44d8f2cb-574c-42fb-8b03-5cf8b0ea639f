<script lang="ts">
    import type { Flyer } from '../../types/flyer';
    import GridView from './GridView.svelte';
    import BookView from './BookView.svelte';
    import { View as ViewGrid, Book } from 'lucide-svelte';
    
    export let flyer: Flyer;
    let viewMode: 'grid' | 'book' = 'grid';
  </script>
  
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-white">{flyer.flyer}</h1>
        <p class="text-gray-400">Valid {new Date(flyer.validFrom).toLocaleDateString()} to {new Date(flyer.validTo).toLocaleDateString()}</p>
      </div>
      
      <div class="flex items-center gap-4">
        <button
          class="flex items-center gap-2 px-4 py-2 rounded-lg transition-colors duration-200
                 {viewMode === 'grid' ? 'bg-primary text-white' : 'bg-dark-lighter text-gray-300 hover:text-white hover:bg-dark-lighter/80'}"
          on:click={() => viewMode = 'grid'}
        >
          <ViewGrid class="w-5 h-5" />
          <span>Grid View</span>
        </button>
        <button
          class="flex items-center gap-2 px-4 py-2 rounded-lg transition-colors duration-200
                 {viewMode === 'book' ? 'bg-primary text-white' : 'bg-dark-lighter text-gray-300 hover:text-white hover:bg-dark-lighter/80'}"
          on:click={() => viewMode = 'book'}
        >
          <Book class="w-5 h-5" />
          <span>Book View</span>
        </button>
      </div>
    </div>
  
    {#if viewMode === 'grid'}
      <GridView items={flyer.items} />
    {:else}
      <BookView items={flyer.items} />
    {/if}
  </div>