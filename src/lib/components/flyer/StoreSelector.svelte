<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { fly } from 'svelte/transition';
  import { Store, MapPin, Clock, ChevronRight } from 'lucide-svelte';
  
  const dispatch = createEventDispatcher<{
    storeSelect: string;
  }>();
  
  interface StoreOption {
    id: string;
    name: string;
    logo: string;
    description: string;
    available: boolean;
    comingSoon?: boolean;
    lastUpdated?: string;
  }
  
  const stores: StoreOption[] = [
    {
      id: 'COSTCOCANADA',
      name: 'Costco Canada',
      logo: '/store-logos/costco.svg',
      description: 'Weekly warehouse deals and instant savings',
      available: true,
      lastUpdated: 'Updated daily'
    },
    {
      id: 'COSTCOUSA',
      name: 'Costco USA',
      logo: '/store-logos/costco.svg',
      description: 'US warehouse deals and member savings',
      available: false,
      comingSoon: true
    },
    {
      id: 'WALMART',
      name: 'Walmart',
      logo: '/store-logos/walmart.svg',
      description: 'Rollback prices and weekly specials',
      available: false,
      comingSoon: true
    },
    {
      id: 'NOFRILLS',
      name: 'No Frills',
      logo: '/store-logos/nofrills.svg',
      description: 'PC Optimum points and weekly deals',
      available: false,
      comingSoon: true
    },
    {
      id: 'LOBLAWS',
      name: 'Loblaws',
      logo: '/store-logos/loblaws.svg',
      description: 'Fresh deals and PC Optimum offers',
      available: false,
      comingSoon: true
    },
    {
      id: 'METRO',
      name: 'Metro',
      logo: '/store-logos/metro.svg',
      description: 'Fresh food and weekly specials',
      available: false,
      comingSoon: true
    }
  ];
  
  function handleStoreClick(store: StoreOption) {
    if (store.available) {
      dispatch('storeSelect', store.id);
    }
  }
</script>

<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
  {#each stores as store, index}
    <div
      class="group relative bg-dark-lighter/50 rounded-xl border border-gray-800/50 backdrop-blur-sm overflow-hidden
             transition-all duration-300 hover:border-gray-700 hover:bg-dark-lighter/70
             {store.available ? 'cursor-pointer hover:scale-105' : 'cursor-not-allowed opacity-75'}"
      on:click={() => handleStoreClick(store)}
      on:keydown={(e) => e.key === 'Enter' && handleStoreClick(store)}
      role="button"
      tabindex="0"
      in:fly={{ y: 20, duration: 300, delay: index * 100 }}
    >
      <!-- Coming Soon Badge -->
      {#if store.comingSoon}
        <div class="absolute top-4 right-4 z-10">
          <span class="bg-blue-500/90 text-white text-xs px-3 py-1 rounded-full font-medium backdrop-blur-sm">
            Coming Soon
          </span>
        </div>
      {/if}
      
      <!-- Available Badge -->
      {#if store.available}
        <div class="absolute top-4 right-4 z-10">
          <span class="bg-green-500/90 text-white text-xs px-3 py-1 rounded-full font-medium backdrop-blur-sm flex items-center gap-1">
            <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            Live
          </span>
        </div>
      {/if}
      
      <div class="p-4 sm:p-6">
        <!-- Store Logo and Header -->
        <div class="flex items-center gap-3 sm:gap-4 mb-4">
          <div class="w-12 h-12 sm:w-16 sm:h-16 bg-white rounded-lg flex items-center justify-center p-2 shadow-lg flex-shrink-0">
            {#if store.logo}
              <img src={store.logo} alt="{store.name} logo" class="w-full h-full object-contain" />
            {:else}
              <Store class="w-6 h-6 sm:w-8 sm:h-8 text-gray-600" />
            {/if}
          </div>
          <div class="flex-1 min-w-0">
            <h3 class="text-lg sm:text-xl font-bold text-white group-hover:text-primary transition-colors">
              {store.name}
            </h3>
            <p class="text-gray-400 text-sm leading-tight">
              {store.description}
            </p>
          </div>
          {#if store.available}
            <ChevronRight class="w-5 h-5 text-gray-400 group-hover:text-primary transition-colors flex-shrink-0" />
          {/if}
        </div>
        
        <!-- Store Info -->
        <div class="space-y-2">
          {#if store.available && store.lastUpdated}
            <div class="flex items-center gap-2 text-sm text-gray-500">
              <Clock class="w-4 h-4" />
              <span>{store.lastUpdated}</span>
            </div>
          {/if}
          
          {#if store.available}
            <div class="flex items-center gap-2 text-sm text-green-400">
              <MapPin class="w-4 h-4" />
              <span>Flyer available now</span>
            </div>
          {:else}
            <div class="flex items-center gap-2 text-sm text-gray-500">
              <MapPin class="w-4 h-4" />
              <span>Flyer not available yet</span>
            </div>
          {/if}
        </div>
        
        <!-- Hover Effect Overlay -->
        {#if store.available}
          <div class="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
        {/if}
      </div>
    </div>
  {/each}
</div>

<!-- Info Section -->
<div class="mt-12 text-center">
  <div class="bg-dark-lighter/30 rounded-lg border border-gray-800/50 backdrop-blur-sm p-6 max-w-2xl mx-auto">
    <h3 class="text-lg font-semibold text-white mb-2">More Stores Coming Soon!</h3>
    <p class="text-gray-400">
      We're working hard to bring you flyers from all your favorite stores. 
      Check back regularly for updates, or 
      <a href="mailto:<EMAIL>" class="text-primary hover:text-primary-light transition-colors">
        let us know
      </a> 
      which stores you'd like to see next.
    </p>
  </div>
</div>


