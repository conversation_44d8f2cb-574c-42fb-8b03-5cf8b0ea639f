<script lang="ts">
  import Button from './Button.svelte';
  import AuthModal from './AuthModal.svelte';
  import AddProductModal from './AddProductModal.svelte';
  import CelebrationModal from './CelebrationModal.svelte';
  import { isValidUrl, isAllowedDomain, getValidationMessage } from '../utils/validation';
  import { user } from '../stores/auth';
  import { page } from '../stores/navigation';
  
  let url = '';
  let isLoading = false;
  let error = '';
  let showAuthModal = false;
  let showAddProductModal = false;
  let showCelebration = false;
  let loadingMessage = '';
  
  async function handleSubmit() {
    error = '';
    if (!url) return;
    
    const validationMessage = getValidationMessage(url, !!$user);
    if (validationMessage) {
      error = validationMessage;
      if (!$user) {
        showAuthModal = true;
      }
      return;
    }
    
    showAddProductModal = true;
  }

  function handleAuthSuccess() {
    showAuthModal = false;
    if (url) {
      showAddProductModal = true;
    }
  }

  function handleProductSuccess() {
    showAddProductModal = false;
    showCelebration = true;
    setTimeout(() => {
      showCelebration = false;
      page.set('dashboard');
    }, 2000);
  }
</script>

<div class="w-full max-w-2xl">
  <form on:submit|preventDefault={handleSubmit} class="relative">
    <input
      type="url"
      bind:value={url}
      placeholder="Paste your URL here..."
      class="w-full px-4 py-3 rounded-lg border border-gray-700 bg-dark-lighter focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-md text-gray-100 text-lg placeholder-gray-400"
      required
    />
    <div class="absolute right-2 top-1/2 transform -translate-y-1/2">
      <Button type="submit" disabled={isLoading}>
        {isLoading ? loadingMessage : 'Analyze'}
      </Button>
    </div>
  </form>
  {#if error}
    <p class="mt-2 text-red-500 text-sm">{error}</p>
  {/if}
</div>

<AuthModal
  show={showAuthModal}
  on:close={() => showAuthModal = false}
  on:success={handleAuthSuccess}
/>

<AddProductModal
  show={showAddProductModal}
  initialUrl={url}
  on:close={() => showAddProductModal = false}
  on:success={handleProductSuccess}
/>

<CelebrationModal
  show={showCelebration}
  on:close={() => showCelebration = false}
/>