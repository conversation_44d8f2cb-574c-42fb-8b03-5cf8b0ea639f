<script lang="ts">
    import type { BlogPost } from '../../data/blogPosts';
    import { page } from '../../stores/navigation';
    import { formatDate } from '../../utils/format';
    
    export let post: BlogPost;
  </script>
  
  <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
    <div class="p-6">
      <div class="flex items-center text-sm text-gray-500 mb-2">
        <time datetime={post.date}>{formatDate(post.date)}</time>
        <span class="mx-2">•</span>
        <span>{post.readingTime}</span>
      </div>
      
      <h2 class="text-xl font-bold text-gray-900 mb-2 hover:text-primary transition-colors">
        <button on:click={() => page.set(`blog/${post.slug}`)}>
          {post.title}
        </button>
      </h2>
      
      <p class="text-gray-600 mb-4">
        {post.description}
      </p>
      
      <div class="flex items-center text-sm">
        <span class="text-gray-600">By {post.author}</span>
      </div>
    </div>
  </article>