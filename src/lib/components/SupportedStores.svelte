<script>
  import { fade } from 'svelte/transition';
</script>

<div 
  class="mt-8 mb-12 text-center"
  in:fade={{ duration: 300, delay: 200 }}
>
  <p class="text-blue-200/60 text-sm uppercase tracking-wider mb-6">Currently Supporting</p>
  <div class="flex flex-wrap gap-8 justify-center mb-12">
    <div class="inline-flex items-center justify-center px-8 py-3 bg-white/5 backdrop-blur-sm rounded-lg">
      <span
        class="text-white font-[Futura] text-2xl tracking-wider"
        style="font-feature-settings: 'kern' 1, 'liga' 1;"
      >
        COSTCO
      </span>
    </div>
    <div class="inline-flex items-center justify-center px-8 py-3 bg-white/5 backdrop-blur-sm rounded-lg">
      <span
        class="text-white font-[Futura] text-2xl tracking-wider"
        style="font-feature-settings: 'kern' 1, 'liga' 1;"
      >
        WAYFAIR
      </span>
    </div>
    <div class="inline-flex items-center justify-center px-8 py-3 bg-white/5 backdrop-blur-sm rounded-lg">
      <span
        class="text-white walmart-text text-2xl tracking-wider"
        style="font-feature-settings: 'kern' 1, 'liga' 1;"
      >
        WALMART
      </span>
    </div>
  </div>
</div>

<style>
  @font-face {
    font-family: 'Futura';
    src: url('https://fonts.cdnfonts.com/css/futura-pt') format('woff2');
    font-display: swap;
    font-weight: normal;
    font-style: normal;
  }

  /* Fallback for Walmart text if Futura fails to load */
  .walmart-text {
    font-family: 'Futura', 'Inter var', system-ui, -apple-system, sans-serif;
  }
</style>