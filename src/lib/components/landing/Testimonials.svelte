<script lang="ts">
    import { Star } from 'lucide-svelte';
    
    const testimonials = [
      {
        name: '<PERSON>',
        role: 'Costco Shopper',
        content: 'Saved over $200 on my furniture purchase thanks to BargainHawk\'s price tracking. The notifications are instant and the interface is super easy to use.',
        rating: 5
      },
      {
        name: '<PERSON>',
        role: 'Smart Shopper',
        content: 'This tool is a game-changer for Costco shopping. I no longer have to manually check prices, and I\'ve saved hundreds of dollars with price adjustments.',
        rating: 5
      },
      {
        name: '<PERSON>',
        role: 'Regular Customer',
        content: '<PERSON><PERSON>n<PERSON><PERSON><PERSON> has completely changed how I shop at Costco. The price history feature helps me make better buying decisions.',
        rating: 5
      }
    ];
  </script>
  
  <section class="py-24 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="text-4xl font-bold text-gray-900">
          What Our Users Say
        </h2>
        <p class="mt-4 text-xl text-gray-600">
          Join thousands of happy shoppers saving money with BargainHawk
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        {#each testimonials as testimonial}
          <div class="bg-gray-50 p-6 rounded-xl">
            <div class="flex gap-1 mb-4">
              {#each Array(testimonial.rating) as _}
                <Star class="w-5 h-5 text-yellow-400 fill-current" />
              {/each}
            </div>
            <p class="text-gray-600 mb-4">"{testimonial.content}"</p>
            <div>
              <p class="font-semibold text-gray-900">{testimonial.name}</p>
              <p class="text-sm text-gray-500">{testimonial.role}</p>
            </div>
          </div>
        {/each}
      </div>
    </div>
  </section>