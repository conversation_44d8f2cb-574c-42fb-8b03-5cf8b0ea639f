<script lang="ts">
  const steps = [
    {
      number: '1',
      title: 'Paste Product URL',
      description: 'Simply copy the URL of any Costco, Walmart, or Wayfair product you want to track.'
    },
    {
      number: '2',
      title: 'Set Your Target Price',
      description: 'Enter the price you paid or want to pay for the item.'
    },
    {
      number: '3',
      title: 'Get Notified',
      description: 'Receive instant email notifications when prices drop below your target.'
    },
    {
      number: '4',
      title: 'Claim Your Savings',
      description: 'Request a price adjustment within the 30-day window and save money.'
    }
  ];
</script>

<section class="py-16 md:py-24 gradient-bg border-t border-gray-800/50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12 md:mb-16">
      <h2 class="text-3xl md:text-4xl font-bold text-gray-100">
        How It Works
      </h2>
      <p class="mt-4 text-lg md:text-xl text-gray-300">
        Start saving money in four simple steps
      </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 md:gap-6">
      {#each steps as step}
        <div class="relative">
          <div class="absolute -top-4 -left-4 w-12 h-12 bg-primary text-white rounded-full 
                      flex items-center justify-center font-bold shadow-glow z-10">
            {step.number}
          </div>
          <div class="pt-8 p-6 bg-dark-lighter/50 backdrop-blur-sm rounded-xl border border-gray-700/50
                      hover:border-gray-600 transition-all duration-300">
            <h3 class="text-lg md:text-xl font-semibold text-gray-100 mb-2">
              {step.title}
            </h3>
            <p class="text-sm md:text-base text-gray-300">
              {step.description}
            </p>
          </div>
        </div>
      {/each}
    </div>
  </div>
</section>