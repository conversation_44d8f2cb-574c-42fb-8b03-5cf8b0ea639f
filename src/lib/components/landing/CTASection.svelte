  <script lang="ts">
    import Button from '../ui/Button.svelte';
    import { user } from '../../stores/auth';
    import { page } from '../../stores/navigation';

    function handleAction() {
      if ($user) {
        page.set('dashboard');
      } else {
        page.set('auth');
      }
    }
  </script>

  <section class="py-16 md:py-24 gradient-bg border-t border-gray-800/50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="text-3xl md:text-4xl font-bold text-white mb-4 md:mb-6">
        {$user ? 'Ready to Track More Items?' : 'Start Saving Today'}
      </h2>
      <p class="text-lg md:text-xl text-gray-300 mb-6 md:mb-8 max-w-2xl mx-auto">
        {#if $user}
          Add more items to your tracking list and maximize your savings potential.
        {:else}
          Join thousands of smart shoppers who never miss a price drop. Sign up now and start tracking your first item for free.
        {/if}
      </p>
      <Button 
        variant="primary"
        customClass="text-base md:text-lg px-6 md:px-8 py-3 md:py-4 shadow-glow hover:shadow-lg transform hover:-translate-y-0.5 transition-all font-semibold w-full md:w-auto"
        on:click={handleAction}
      >
        {$user ? 'Go to Dashboard' : 'Get Started - It\'s Free'}
      </Button>
    </div>
  </section>