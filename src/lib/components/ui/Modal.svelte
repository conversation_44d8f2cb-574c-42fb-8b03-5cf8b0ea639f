<script lang="ts">
    import { fade } from 'svelte/transition';
    import { X } from 'lucide-svelte';
    
    export let show = false;
    export let maxWidth = 'max-w-md';
  </script>
  
  {#if show}
    <div 
      class="fixed inset-0 z-50 overflow-y-auto"
      transition:fade={{ duration: 200 }}
    >
      <!-- Backdrop -->
      <div class="fixed inset-0 bg-black/50 backdrop-blur-sm"></div>
      
      <!-- Modal -->
      <div class="relative min-h-screen flex items-center justify-center p-4">
        <div 
          class="relative {maxWidth} w-full bg-gradient-to-br from-primary/10 to-dark-lighter 
                 rounded-lg border border-gray-800/50 shadow-xl backdrop-blur-sm"
          transition:fade={{ duration: 300, delay: 100 }}
        >
          <!-- Close button -->
          <button
            class="absolute top-4 right-4 p-1 text-gray-400 hover:text-white transition-colors"
            on:click
            aria-label="Close modal"
          >
            <X size={20} />
          </button>
          
          <slot />
        </div>
      </div>
    </div>
  {/if}