<script lang="ts">
    export let type: string = "text";
    export let label: string;
    export let value = "";
    export let required = false;
    export let placeholder = "";
  
    function handleInput(event: Event) {
      const target = event.target as HTMLInputElement;
      value = target.value;
    }
  </script>
  
  <label class="block">
    <span class="text-sm font-medium text-gray-300">{label}</span>
    <input
      {type}
      {required}
      {placeholder}
      {value}
      on:input={handleInput}
      class="mt-1.5 block w-full px-4 py-3 rounded-lg bg-dark border border-gray-700 
             text-white placeholder-gray-400 text-base
             focus:border-primary focus:ring-1 focus:ring-primary
             hover:border-gray-600 transition-colors"
    />
  </label>