  <script lang="ts">
    export let type: "button" | "submit" = "button";
    export let variant: "primary" | "secondary" = "primary";
    export let disabled = false;
    export let fullWidth = false;
    export let customClass = ""; // Changed from class to customClass

    const baseStyles = "px-4 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed";
    const variants = {
      primary: "bg-primary hover:bg-primary-dark text-white shadow-md hover:shadow-lg",
      secondary: "bg-dark border border-gray-700 hover:border-gray-600 text-white"
    };
  </script>

  <button
    {type}
    {disabled}
    class="{baseStyles} {variants[variant]} {fullWidth ? 'w-full' : ''} {customClass}"
    on:click
  >
    <slot />
  </button>