<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Button from '../ui/Button.svelte';
  import Input from '../ui/Input.svelte';
  import PasswordInput from './PasswordInput.svelte';
  import { signIn, signUp } from '../../stores/auth';
  
  export let isLogin: boolean;
  
  const dispatch = createEventDispatcher();
  let email = '';
  let password = '';
  let error = '';
  let loading = false;

  async function handleSubmit() {
    if (!email || !password) return;
    
    try {
      loading = true;
      error = '';
      
      if (isLogin) {
        await signIn(email, password);
        dispatch('success');
      } else {
        await signUp(email, password);
        dispatch('signup', { email });
      }
    } catch (e) {
      error = e.message;
    } finally {
      loading = false;
    }
  }
</script>

<form on:submit|preventDefault={handleSubmit} class="space-y-4">
  <Input
    type="email"
    label="Email"
    bind:value={email}
    required
    placeholder="<EMAIL>"
  />
  
  <PasswordInput
    label="Password"
    bind:value={password}
  />
  
  {#if error}
    <p class="text-red-400 text-sm">{error}</p>
  {/if}
  
  <div class="flex flex-col space-y-3">
    <Button type="submit" disabled={loading} fullWidth>
      {loading ? 'Loading...' : isLogin ? 'Sign In' : 'Create Account'}
    </Button>
    
    {#if isLogin}
      <button
        type="button"
        class="text-sm text-gray-400 hover:text-white transition-colors"
        on:click={() => dispatch('forgotPassword')}
      >
        Forgot your password?
      </button>
    {/if}
    
    <button
      type="button"
      class="text-sm text-gray-400 hover:text-white transition-colors"
      on:click={() => dispatch('toggle')}
    >
      {isLogin ? "Don't have an account? Sign up" : 'Already have an account? Sign in'}
    </button>
  </div>
</form>