  <script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import Modal from '../ui/Modal.svelte';
    import AuthForm from './AuthForm.svelte';
    import ForgotPasswordForm from './ForgotPasswordForm.svelte';
    import ConfirmationMessage from './ConfirmationMessage.svelte';
    
    export let show = false;
    
    const dispatch = createEventDispatcher();
    let isLogin = true;
    let showConfirmation = false;
    let showForgotPassword = false;
    let confirmedEmail = '';

    function handleClose() {
      showConfirmation = false;
      showForgotPassword = false;
      isLogin = true;
      dispatch('close');
    }

    function handleAuthSuccess() {
      dispatch('success');
      handleClose();
    }

    function handleSignupSuccess(event: CustomEvent) {
      confirmedEmail = event.detail?.email;
      showConfirmation = true;
    }

    function handleForgotPassword() {
      showForgotPassword = true;
    }

    function handleBackToSignIn() {
      showForgotPassword = false;
    }

    $: if (!show) {
      showConfirmation = false;
      showForgotPassword = false;
    }
  </script>

  <Modal {show} on:click={handleClose}>
    <div class="p-6">
      {#if showConfirmation}
        <ConfirmationMessage 
          email={confirmedEmail} 
          on:close={handleClose} 
        />
      {:else if showForgotPassword}
        <div>
          <h2 class="text-2xl font-bold text-white mb-6">Reset Password</h2>
          <ForgotPasswordForm
            on:close={handleClose}
            on:back={handleBackToSignIn}
          />
        </div>
      {:else}
        <h2 class="text-2xl font-bold text-white mb-6">
          {isLogin ? 'Welcome Back' : 'Create Account'}
        </h2>
        
        <AuthForm 
          {isLogin}
          on:success={handleAuthSuccess}
          on:signup={handleSignupSuccess}
          on:toggle={() => isLogin = !isLogin}
          on:forgotPassword={handleForgotPassword}
        />
      {/if}
    </div>
  </Modal>