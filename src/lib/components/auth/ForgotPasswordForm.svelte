  <script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import Button from '../ui/Button.svelte';
    import Input from '../ui/Input.svelte';
    import { supabase } from '../../utils/supabase';
    
    const dispatch = createEventDispatcher();
    let email = '';
    let error = '';
    let loading = false;
    let success = false;

    async function handleSubmit() {
      if (!email) return;
      
      try {
        loading = true;
        error = '';
        
        console.log('Sending reset email to:', email);
        console.log('Redirect URL:', `${window.location.origin}/forgot-password`);

        const { error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
          redirectTo: `${window.location.origin}/forgot-password`
        });

        console.log('Reset email result:', { error: resetError });
        
        if (resetError) throw resetError;
        
        success = true;
      } catch (e) {
        error = e instanceof Error ? e.message : 'Failed to send reset instructions';
      } finally {
        loading = false;
      }
    }
  </script>

  {#if success}
    <div class="text-center">
      <h2 class="text-2xl font-bold text-white mb-4">Check Your Email</h2>
      <p class="text-gray-300 mb-6">
        We've sent password reset instructions to <strong class="text-white">{email}</strong>
      </p>
      <p class="text-sm text-gray-400 mb-4">
        Don't see the email? Check your spam folder.
      </p>
      <Button on:click={() => dispatch('close')} fullWidth>Close</Button>
    </div>
  {:else}
    <form on:submit|preventDefault={handleSubmit} class="space-y-4">
      <Input
        type="email"
        label="Email"
        bind:value={email}
        required
        placeholder="<EMAIL>"
      />
      
      {#if error}
        <p class="text-red-400 text-sm">{error}</p>
      {/if}
      
      <div class="flex flex-col space-y-3">
        <Button type="submit" disabled={loading} fullWidth>
          {loading ? 'Sending...' : 'Send Reset Instructions'}
        </Button>
        
        <button
          type="button"
          class="text-sm text-gray-400 hover:text-white transition-colors"
          on:click={() => dispatch('back')}
        >
          Back to Sign In
        </button>
      </div>
    </form>
  {/if}