<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Button from '../ui/Button.svelte';
  
  export let email: string;
  
  const dispatch = createEventDispatcher();
</script>

<div class="text-center">
  <h2 class="text-2xl font-bold text-white mb-4">Check Your Email</h2>
  <p class="text-gray-300 mb-6">
    We've sent a confirmation link to <strong class="text-white">{email}</strong>. Please check your email and click the link to verify your account.
  </p>
  <p class="text-sm text-gray-400 mb-4">
    Don't see the email? Check your spam folder or try signing up again.
  </p>
  <Button on:click={() => dispatch('close')} fullWidth>Close</Button>
</div>