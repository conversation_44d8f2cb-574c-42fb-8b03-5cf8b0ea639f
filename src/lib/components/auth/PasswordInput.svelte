<script lang="ts">
  import { Eye, EyeOff } from 'lucide-svelte';
  
  export let value: string;
  export let label = "Password";
  export let required = true;
  
  let showPassword = false;
  let inputEl: HTMLInputElement;

  function toggleVisibility() {
    showPassword = !showPassword;
    if (inputEl) {
      inputEl.type = showPassword ? 'text' : 'password';
      inputEl.focus();
    }
  }
</script>

<label class="block">
  <span class="text-sm font-medium text-gray-300">{label}</span>
  <div class="relative">
    <input
      type="password"
      bind:this={inputEl}
      bind:value
      {required}
      placeholder="••••••••"
      class="mt-1.5 block w-full px-4 py-3 rounded-lg bg-dark border border-gray-700 
             text-white placeholder-gray-400 text-base
             focus:border-primary focus:ring-1 focus:ring-primary
             hover:border-gray-600 transition-colors"
    />
    <button
      type="button"
      class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors"
      on:click={toggleVisibility}
      aria-label={showPassword ? "Hide password" : "Show password"}
    >
      <svelte:component this={showPassword ? EyeOff : Eye} class="w-5 h-5" />
    </button>
  </div>
</label>