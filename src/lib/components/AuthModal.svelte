<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import CloseButton from './CloseButton.svelte';
  import { AuthForm, ConfirmationMessage } from './auth';
  
  export let show = false;
  
  const dispatch = createEventDispatcher();
  let isLogin = true;
  let showConfirmationMessage = false;
  let confirmedEmail = '';

  function handleClose() {
    showConfirmationMessage = false;
    isLogin = true;
    dispatch('close');
  }

  function handleAuthSuccess() {
    dispatch('success');
    handleClose();
  }

  function handleSignupSuccess(event: CustomEvent) {
    confirmedEmail = event.detail?.email;
    showConfirmationMessage = true;
  }

  $: if (!show) {
    showConfirmationMessage = false;
  }
</script>

{#if show}
  <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg p-6 w-full max-w-md relative">
      <CloseButton on:click={handleClose} />
      
      {#if showConfirmationMessage}
        <ConfirmationMessage 
          email={confirmedEmail} 
          on:close={handleClose} 
        />
      {:else}
        <h2 class="text-2xl font-bold mb-4">{isLogin ? 'Sign In' : 'Create Account'}</h2>
        <AuthForm 
          {isLogin}
          on:success={handleAuthSuccess}
          on:signup={handleSignupSuccess}
          on:toggle={() => isLogin = !isLogin}
        />
      {/if}
    </div>
  </div>
{/if}