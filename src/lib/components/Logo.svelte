<script lang="ts">
  import { page } from '../stores/navigation';
  
  export let size: "sm" | "md" | "lg" = "md";
  
  const sizes = {
    sm: "text-xl",
    md: "text-2xl",
    lg: "text-3xl"
  };

  function goToHome() {
    page.set('home');
  }
</script>

<button
  on:click={goToHome}
  class="{sizes[size]} font-bold text-primary hover:text-primary/90 transition-colors duration-200"
>
  BargainHawk
</button>