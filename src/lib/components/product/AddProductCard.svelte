<script lang="ts">
    import { Plus, Lock } from 'lucide-svelte';
    import { fly } from 'svelte/transition';
    import { createEventDispatcher } from 'svelte';

    export let isAtProductLimit: boolean = false;

    const dispatch = createEventDispatcher();
    let isHovered = false;

    function handleKeydown(event: KeyboardEvent) {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        dispatch('click');
      }
    }
  </script>
  
  <div
    class="bg-dark-lighter/50 rounded-lg border border-gray-800/50 backdrop-blur-sm overflow-hidden
           {isAtProductLimit ? 'opacity-60' : 'hover:border-primary/50 cursor-pointer'}
           transition-all duration-300 h-[400px] flex flex-col"
    on:mouseenter={() => !isAtProductLimit && (isHovered = true)}
    on:mouseleave={() => isHovered = false}
    on:click={() => dispatch('click')}
    on:keydown={handleKeydown}
    in:fly={{ y: 20, duration: 300 }}
    role="button"
    tabindex="0"
    aria-label={isAtProductLimit ? "Product limit reached" : "Add new product"}
  >
    <div class="relative h-[250px] overflow-hidden bg-dark/50 flex-shrink-0 flex items-center justify-center">
      <div class="transform transition-transform duration-300 {isHovered && !isAtProductLimit ? 'scale-110' : ''}">
        <div class="p-6 rounded-full {isAtProductLimit ? 'bg-gray-600/10 border-gray-600/20' : 'bg-primary/10 border-primary/20'}">
          {#if isAtProductLimit}
            <Lock class="w-12 h-12 text-gray-500" />
          {:else}
            <Plus class="w-12 h-12 text-primary" />
          {/if}
        </div>
      </div>
    </div>

    <div class="p-4 flex-grow flex flex-col items-center justify-center text-center">
      <h3 class="font-medium {isAtProductLimit ? 'text-gray-400' : 'text-white'} mb-2">
        {isAtProductLimit ? 'Product Limit Reached' : 'Add New Product'}
      </h3>
      <p class="text-sm text-gray-400">
        {isAtProductLimit ? '5 products maximum (click for details)' : 'Track prices and get notified of drops'}
      </p>
    </div>
  </div>