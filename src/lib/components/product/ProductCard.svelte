<script lang="ts">
  import { formatPrice } from '../../utils/format';
  import type { Product } from '../../types';
  import { Trash2, ExternalLink, Pencil, Check, X } from 'lucide-svelte';
  import { fade, fly } from 'svelte/transition';
  import { provinces, states } from '../../utils/locations';
  import { updateProduct } from '../../stores/products';
  
  export let product: Product;
  export let onDelete: (id: string) => void;
  
  let isHovered = false;
  let imageLoaded = false;
  let imageError = false;
  let isEditingPrice = false;
  let isEditingLocation = false;
  let editPrice = product.userPrice?.toString() || '';
  let editLocation = product.province || '';
  let error = '';
  let loading = false;

  $: isUSStore = product.url.includes('costco.com') || product.url.includes('wayfair.com');
  $: locations = isUSStore ? states : provinces;
  $: locationLabel = isUSStore ? 'State' : 'Province';
  $: isCostco = product.url.includes('costco.ca') || product.url.includes('costco.com');

  function handleImageError() {
    imageError = true;
  }

  function startEditingPrice() {
    editPrice = product.userPrice?.toString() || '';
    isEditingPrice = true;
  }

  function startEditingLocation() {
    editLocation = product.province || locations[0]?.code || '';
    isEditingLocation = true;
  }

  function cancelEditing() {
    isEditingPrice = false;
    isEditingLocation = false;
    error = '';
  }

  async function savePrice() {
    const price = parseFloat(editPrice);
    if (isNaN(price) || price <= 0) {
      error = 'Please enter a valid price';
      return;
    }

    try {
      loading = true;
      error = '';
      await updateProduct(product.id, price, product.province || '');
      product.userPrice = price;
      isEditingPrice = false;
    } catch (e) {
      error = e instanceof Error ? e.message : 'Failed to update price';
    } finally {
      loading = false;
    }
  }

  async function saveLocation() {
    if (!editLocation) {
      error = `Please select your ${locationLabel.toLowerCase()}`;
      return;
    }

    try {
      loading = true;
      error = '';
      await updateProduct(product.id, product.userPrice || 0, editLocation);
      product.province = editLocation;
      isEditingLocation = false;
    } catch (e) {
      error = e instanceof Error ? e.message : 'Failed to update location';
    } finally {
      loading = false;
    }
  }

  function handlePriceInput(event: Event) {
    const input = event.target as HTMLInputElement;
    let value = input.value.replace(/[^\d.]/g, '');
    
    const parts = value.split('.');
    if (parts.length > 2) {
      value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    if (parts[1]?.length > 2) {
      value = parts[0] + '.' + parts[1].slice(0, 2);
    }
    
    editPrice = value;
  }
</script>

<div
  class="bg-dark-lighter/50 rounded-lg border border-gray-800/50 backdrop-blur-sm overflow-hidden
         hover:border-gray-700 transition-all duration-300 h-[400px] flex flex-col"
  on:mouseenter={() => isHovered = true}
  on:mouseleave={() => isHovered = false}
  in:fly={{ y: 20, duration: 300 }}
  role="article"
  aria-label="Product card for {product.productName}"
>
  <div class="relative h-[250px] overflow-hidden bg-dark/50 flex-shrink-0">
    {#if !imageError}
      <img
        src={product.imageUrl}
        alt={product.productName}
        class="w-full h-full object-contain transform transition-transform duration-300 {isHovered ? 'scale-105' : ''}"
        on:error={handleImageError}
        on:load={() => imageLoaded = true}
      />
    {:else}
      <div class="w-full h-full flex items-center justify-center text-gray-500" role="img" aria-label="No image available">
        <span class="text-sm">No image available</span>
      </div>
    {/if}
    
    {#if isHovered}
      <div 
        class="absolute inset-0 bg-dark/80 backdrop-blur-sm"
        transition:fade={{ duration: 200 }}
        role="group"
        aria-label="Product actions"
      >
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex items-center gap-4">
          <a
            href={product.url}
            target="_blank"
            rel="noopener noreferrer"
            class="p-3 bg-dark-lighter rounded-full hover:bg-dark border border-gray-700 hover:border-gray-600 transition-colors"
            title="View on retailer's site"
            aria-label="View {product.productName} on retailer's site"
          >
            <ExternalLink class="w-5 h-5 text-gray-300" aria-hidden="true" />
          </a>
          <button
            class="p-3 bg-dark-lighter rounded-full hover:bg-red-900/30 border border-gray-700 hover:border-red-500/50 transition-colors"
            on:click={() => onDelete(product.id)}
            title="Remove from tracking"
            aria-label="Remove {product.productName} from tracking"
          >
            <Trash2 class="w-5 h-5 text-red-400" aria-hidden="true" />
          </button>
        </div>
      </div>
    {/if}
  </div>

  <div class="p-4 flex-grow flex flex-col">
    <h3 class="font-medium text-white mb-1 line-clamp-1" title={product.productName}>
      {product.productName}
    </h3>
    
    <div class="mt-auto space-y-2">
      <div class="flex items-baseline justify-between">
        <span class="text-lg font-bold text-primary">
          {formatPrice(product.price)}
        </span>
        {#if product.price < product.userPrice}
          <span class="text-sm text-green-400 font-medium" role="status">
            Price dropped!
          </span>
        {/if}
      </div>
      
      <div class="flex items-center justify-between text-sm text-gray-400">
        <div>
          {#if isEditingPrice}
            <div class="flex items-center gap-2">
              <input
                type="text"
                inputmode="decimal"
                bind:value={editPrice}
                on:input={handlePriceInput}
                class="w-24 px-2 py-1 bg-dark border border-gray-700 rounded text-white text-sm"
                placeholder="0.00"
              />
              <div class="flex gap-1">
                <button
                  class="p-1 hover:bg-gray-700 rounded-full transition-colors"
                  on:click={cancelEditing}
                  disabled={loading}
                  title="Cancel"
                >
                  <X class="w-4 h-4 text-gray-400" />
                </button>
                <button
                  class="p-1 hover:bg-primary/20 rounded-full transition-colors"
                  on:click={savePrice}
                  disabled={loading}
                  title="Save changes"
                >
                  <Check class="w-4 h-4 text-primary" />
                </button>
              </div>
            </div>
          {:else}
            <div class="flex items-center gap-2">
              <span>Target: {formatPrice(product.userPrice)}</span>
              <button
                class="p-1 hover:bg-gray-700 rounded-full transition-colors"
                on:click={startEditingPrice}
                title="Edit target price"
              >
                <Pencil class="w-3 h-3" />
              </button>
            </div>
          {/if}
        </div>
      </div>

      {#if isCostco}
        <div class="text-sm text-gray-400">
          {#if isEditingLocation}
            <div class="flex items-center gap-2">
              <select
                bind:value={editLocation}
                class="flex-grow px-2 py-1 bg-dark border border-gray-700 rounded text-white text-sm"
              >
                <option value="">Select {locationLabel}</option>
                {#each locations as loc}
                  <option value={loc.code}>{loc.code}</option>
                {/each}
              </select>
              <div class="flex gap-1">
                <button
                  class="p-1 hover:bg-gray-700 rounded-full transition-colors"
                  on:click={cancelEditing}
                  disabled={loading}
                  title="Cancel"
                >
                  <X class="w-4 h-4 text-gray-400" />
                </button>
                <button
                  class="p-1 hover:bg-primary/20 rounded-full transition-colors"
                  on:click={saveLocation}
                  disabled={loading}
                  title="Save changes"
                >
                  <Check class="w-4 h-4 text-primary" />
                </button>
              </div>
            </div>
          {:else}
            <div class="flex items-center gap-2">
              <span>Location: {product.province || 'Not set'}</span>
              <button
                class="p-1 hover:bg-gray-700 rounded-full transition-colors"
                on:click={startEditingLocation}
                title="Edit location"
              >
                <Pencil class="w-3 h-3" />
              </button>
            </div>
          {/if}
        </div>
      {/if}

      {#if error}
        <p class="text-red-400 text-xs">{error}</p>
      {/if}
    </div>
  </div>
</div>