<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Modal from '../ui/Modal.svelte';
  import Button from '../ui/Button.svelte';
  import Input from '../ui/Input.svelte';
  import { validateProductUrl } from '../../utils/product/validation';
  import { addProduct } from '../../stores/products';
  import { provinces, states } from '../../utils/locations';
  import { formatPrice } from '../../utils/format';
  import { CircleHelp as HelpCircle } from 'lucide-svelte';
  import { DotLottieSvelte } from '@lottiefiles/dotlottie-svelte';
  
  export let show = false;
  export let initialUrl = '';
  
  const dispatch = createEventDispatcher();
  let url = initialUrl;
  let productName = '';
  let price = '';
  let location = '';
  let error = '';
  let loading = false;
  let loadingMessage = 'Adding product...';

  $: isUSStore = url.includes('costco.com') || url.includes('wayfair.com') || url.includes('walmart.com');
  $: locations = isUSStore ? states : provinces;
  $: locationLabel = isUSStore ? 'State' : 'Province';

  function handlePriceInput(event: Event) {
    const input = event.target as HTMLInputElement;
    let value = input.value.replace(/[^\d.]/g, '');
    
    const parts = value.split('.');
    if (parts.length > 2) {
      value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    if (parts[1]?.length > 2) {
      value = parts[0] + '.' + parts[1].slice(0, 2);
    }
    
    price = value;
  }

  async function handleSubmit() {
    const validationError = validateProductUrl(url);
    if (validationError) {
      error = validationError;
      return;
    }

    if (!productName.trim()) {
      error = 'Please enter a product name';
      return;
    }

    const priceNum = Number(price);
    if (!price || isNaN(priceNum) || priceNum <= 0) {
      error = 'Please enter a valid target price';
      return;
    }

    if (!location) {
      error = `Please select your ${locationLabel.toLowerCase()}`;
      return;
    }
    
    try {
      loading = true;
      error = '';
      
      // Round to 2 decimal places before sending
      const roundedPrice = Math.round(priceNum * 100) / 100;
      
      // Start API request
      const addProductPromise = addProduct(url, roundedPrice, productName, location);
      
      // Show loading states while API request is in progress
      loadingMessage = 'Adding product...';
      await new Promise(resolve => setTimeout(resolve, 2000));
      loadingMessage = 'Almost done...';
      
      // Wait for API request to complete
      await addProductPromise;
      
      dispatch('success');
      handleClose();
    } catch (e) {
      error = e instanceof Error ? e.message : 'Failed to add product';
    } finally {
      loading = false;
      loadingMessage = 'Adding product...';
    }
  }

  function handleClose() {
    url = '';
    productName = '';
    price = '';
    location = '';
    error = '';
    dispatch('close');
  }

  $: if (show && initialUrl) {
    url = initialUrl;
  }
</script>

<Modal {show} on:click={handleClose}>
  {#if loading}
    <div class="p-6 text-center">
      <div class="w-48 h-48 mx-auto mb-4">
        <DotLottieSvelte
          src="https://lottie.host/e4e44af2-04b4-484f-8e66-2f0bbeae4b94/csqesQg1uG.lottie"
          loop
          autoplay
        />
      </div>
      <h3 class="text-xl font-semibold text-white mb-2">{loadingMessage}</h3>
      <p class="text-gray-400 text-sm">Please wait while we process your request...</p>
    </div>
  {:else}
    <div class="p-6">
      <h2 class="text-2xl font-bold text-white mb-6">Add Product</h2>
      
      <form on:submit|preventDefault={handleSubmit} class="space-y-4">
        <Input
          label="Product URL"
          bind:value={url}
          required
          placeholder="https://www.costco.ca/product-url or https://www.walmart.com/ip/..."
          disabled={loading}
        />
        
        <Input
          label="Product Name"
          bind:value={productName}
          required
          placeholder="Enter product name"
          disabled={loading}
        />
        
        <div class="block">
          <div class="flex items-center gap-2 mb-1.5">
            <span class="text-sm font-medium text-gray-300">Target Price</span>
            <div class="relative group">
              <HelpCircle class="w-4 h-4 text-gray-400 cursor-help" />
              <div class="invisible group-hover:visible absolute left-1/2 -translate-x-1/2 bottom-full mb-2 w-64 p-2 bg-gray-800 text-white text-xs rounded shadow-lg z-50">
                Enter the price you either paid for the item or the price you want to pay. If the price drops below this amount, we'll notify you immediately.
                <div class="absolute left-1/2 -translate-x-1/2 top-full w-2 h-2 bg-gray-800 transform rotate-45"></div>
              </div>
            </div>
          </div>
          <input
            type="text"
            inputmode="decimal"
            bind:value={price}
            on:input={handlePriceInput}
            required
            placeholder="0.00"
            disabled={loading}
            class="block w-full px-4 py-3 rounded-lg bg-dark border border-gray-700 
                   text-white text-base
                   focus:border-primary focus:ring-1 focus:ring-primary
                   hover:border-gray-600 transition-colors
                   disabled:opacity-50 disabled:cursor-not-allowed"
          />
        </div>

        <label class="block">
          <span class="text-sm font-medium text-gray-300">{locationLabel}</span>
          <div class="relative">
            <select
              bind:value={location}
              required
              disabled={loading}
              class="mt-1.5 block w-full px-4 py-3 rounded-lg bg-dark border border-gray-700 
                     text-white text-base appearance-none
                     focus:border-primary focus:ring-1 focus:ring-primary
                     hover:border-gray-600 transition-colors
                     disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <option value="">Select {locationLabel}</option>
              {#each locations as loc}
                <option value={loc.code}>{loc.name}</option>
              {/each}
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none text-gray-400">
              <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                <path d="M7 7l3-3 3 3m0 6l-3 3-3-3" stroke="currentColor" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
        </label>
        
        {#if error}
          <p class="text-red-400 text-sm">{error}</p>
        {/if}
        
        <div class="flex justify-end gap-3 pt-2">
          <Button variant="secondary" on:click={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            Add Product
          </Button>
        </div>
      </form>
    </div>
  {/if}
</Modal>