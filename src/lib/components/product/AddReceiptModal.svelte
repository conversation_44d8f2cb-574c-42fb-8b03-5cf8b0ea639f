<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import Modal from '../ui/Modal.svelte';
    import Button from '../ui/Button.svelte';
    import { validateReceiptFile, uploadReceipt } from '../../utils/receipt';
    import { Upload, TriangleAlert as AlertTriangle, ExternalLink } from 'lucide-svelte';
    
    export let show = false;
    
    const dispatch = createEventDispatcher();
    let fileInput: HTMLInputElement;
    let selectedFile: File | null = null;
    let error = '';
    let loading = false;
    let dragOver = false;
  
    function handleFileSelect(event: Event) {
      const input = event.target as HTMLInputElement;
      if (input.files?.length) {
        const file = input.files[0];
        error = validateReceiptFile(file);
        if (!error) {
          selectedFile = file;
        }
      }
    }
  
    function handleDragOver(event: DragEvent) {
      event.preventDefault();
      dragOver = true;
    }
  
    function handleDragLeave() {
      dragOver = false;
    }
  
    function handleDrop(event: DragEvent) {
      event.preventDefault();
      dragOver = false;
      
      if (event.dataTransfer?.files.length) {
        const file = event.dataTransfer.files[0];
        error = validateReceiptFile(file);
        if (!error) {
          selectedFile = file;
        }
      }
    }
  
    async function handleSubmit() {
      if (!selectedFile) {
        error = 'Please select a file';
        return;
      }
  
      try {
        loading = true;
        error = '';
        
        await uploadReceipt(selectedFile);
        dispatch('success');
        handleClose();
      } catch (e) {
        error = e instanceof Error ? e.message : 'Failed to upload receipt';
      } finally {
        loading = false;
      }
    }
  
    function handleClose() {
      selectedFile = null;
      error = '';
      if (fileInput) fileInput.value = '';
      dispatch('close');
    }
  </script>
  
  <Modal {show} on:click={handleClose}>
    <div class="p-6">
      <h2 class="text-2xl font-bold text-white mb-6">Upload Costco Receipt</h2>
      
      <form on:submit|preventDefault={handleSubmit} class="space-y-4">
        <div
          class="relative border-2 border-dashed rounded-lg p-8 text-center
                 {dragOver ? 'border-primary bg-primary/5' : 'border-gray-700 hover:border-gray-600'}
                 transition-colors"
          on:dragover={handleDragOver}
          on:dragleave={handleDragLeave}
          on:drop={handleDrop}
          role="button"
          tabindex="0"
          aria-label="Drop receipt file here or click to select"
        >
          <input
            type="file"
            bind:this={fileInput}
            on:change={handleFileSelect}
            accept=".jpg,.jpeg,.png"
            class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          />
          
          {#if selectedFile}
            <div class="text-white">
              <p class="font-medium mb-1">{selectedFile.name}</p>
              <p class="text-sm text-gray-400">
                {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
              </p>
            </div>
          {:else}
            <div class="text-gray-400">
              <Upload class="w-8 h-8 mx-auto mb-2" />
              <p class="font-medium">Drop your receipt here or click to browse</p>
              <p class="text-sm mt-1">Supports JPEG and PNG (max 5MB)</p>
            </div>
          {/if}
        </div>
        
        {#if error}
          <p class="text-red-400 text-sm">{error}</p>
        {/if}
  
        <!-- Guide Link -->
        <a
          href="/blog/how-to-access-costco-receipts"
          target="_blank"
          rel="noopener noreferrer"
          class="flex items-center gap-2 text-primary hover:text-primary-dark transition-colors text-sm"
        >
          <ExternalLink class="w-4 h-4" />
          <span>Check out our guide on how to access your Costco receipts</span>
        </a>
  
        <!-- Beta Notice -->
        <div class="flex items-start gap-2 p-3 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
          <AlertTriangle class="w-5 h-5 text-yellow-400 flex-shrink-0 mt-0.5" />
          <div class="text-sm text-yellow-300">
            <p class="font-medium mb-1">Beta Feature</p>
            <p>This feature is currently in beta. We're actively working on improving our receipt processing capabilities. Your feedback is valuable to us!</p>
          </div>
        </div>
        
        <div class="flex justify-end gap-3 pt-2">
          <Button variant="secondary" on:click={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading || !selectedFile}>
            {loading ? 'Uploading...' : 'Upload Receipt'}
          </Button>
        </div>
      </form>
    </div>
  </Modal>