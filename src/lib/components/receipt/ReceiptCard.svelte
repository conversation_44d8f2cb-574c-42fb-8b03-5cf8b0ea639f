<script lang="ts">
    import type { UploadMetadata } from '../../types/receipt';
    import { createEventDispatcher } from 'svelte';
    import { FileText, Clock } from 'lucide-svelte';
    
    export let receipt: UploadMetadata;
    
    const dispatch = createEventDispatcher();
  
    function handleClick() {
      dispatch('click', receipt);
    }
  
    function formatDate(dateString: string): string {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  
    $: statusColors = {
      pending: 'bg-yellow-900/20 text-yellow-400 border border-yellow-400/20',
      failed: 'bg-red-900/20 text-red-400 border border-red-400/20',
      complete: 'bg-green-900/20 text-green-400 border border-green-400/20'
    };
  </script>
  
  <button
    class="w-full text-left bg-dark-lighter/50 rounded-lg border border-gray-800/50 backdrop-blur-sm
           hover:border-gray-700 transition-all duration-300 p-4 flex items-center gap-4"
    on:click={handleClick}
  >
    <div class="p-3 bg-primary/10 rounded-lg">
      <FileText class="w-6 h-6 text-primary" />
    </div>
    
    <div class="flex-grow">
      <div class="text-white font-medium">Receipt #{receipt.id.slice(-6)}</div>
      <div class="flex items-center gap-1 text-sm text-gray-400">
        <Clock class="w-4 h-4" />
        <span>Uploaded {formatDate(receipt.uploadTime)}</span>
      </div>
    </div>
    
    <div class="px-3 py-1 rounded-full text-xs font-medium {statusColors[receipt.status] || statusColors.failed}">
      {receipt.status}
    </div>
  </button>