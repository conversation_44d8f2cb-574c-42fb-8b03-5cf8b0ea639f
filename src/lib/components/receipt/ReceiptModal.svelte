<script lang="ts">
    import { onMount } from 'svelte';
    import { createEventDispatcher } from 'svelte';
    import type { UploadMetadata, ReceiptData } from '../../types/receipt';
    import Modal from '../ui/Modal.svelte';
    import { getReceiptData } from '../../services/receipts';
    import { formatPrice } from '../../utils/format';
    import { MapPin, Tag, Loader as Loader2 } from 'lucide-svelte';
    
    export let show = false;
    export let receipt: UploadMetadata | null = null;
    
    const dispatch = createEventDispatcher();
    let receiptData: ReceiptData | null = null;
    let loading = false;
    let error = '';
  
    async function loadReceiptData() {
      if (!receipt) return;
      
      try {
        loading = true;
        error = '';
        receiptData = await getReceiptData(receipt.id);
      } catch (e) {
        error = e instanceof Error ? e.message : 'Failed to load receipt data';
      } finally {
        loading = false;
      }
    }
  
    $: if (show && receipt) {
      loadReceiptData();
    }
  
    function handleClose() {
      receiptData = null;
      loading = false;
      error = '';
      dispatch('close');
    }
  </script>
  
  <Modal {show} on:click={handleClose} maxWidth="max-w-6xl">
    <div class="flex h-[80vh]">
      <!-- Left side: Receipt Image -->
      <div class="w-1/2 border-r border-gray-800">
        {#if receipt}
          <iframe
            src={receipt.fileUrl}
            title="Receipt"
            class="w-full h-full"
          ></iframe>
        {/if}
      </div>
      
      <!-- Right side: Receipt Data -->
      <div class="w-1/2 p-6 overflow-y-auto">
        <div class="flex justify-between items-start mb-6">
          <h2 class="text-2xl font-bold text-white">Receipt Details</h2>
        </div>
        
        {#if loading}
          <div class="flex flex-col items-center justify-center h-40 text-gray-400">
            <Loader2 class="w-8 h-8 animate-spin mb-4" />
            <p>Processing receipt data...</p>
          </div>
        {:else if error}
          <div class="bg-red-900/20 border border-red-500/30 rounded-lg p-4 text-red-400">
            {error}
          </div>
        {:else if !receiptData}
          <div class="bg-dark/30 rounded-lg p-6 backdrop-blur-sm">
            <div class="flex items-center justify-center h-40">
              <p class="text-gray-400 text-center">
                We are currently processing this data. If there is a price drop online, 
                we will notify you. Please check back again in a few minutes.
              </p>
            </div>
          </div>
        {:else}
          <!-- Store Information -->
          <div class="bg-dark/30 rounded-lg p-4 backdrop-blur-sm mb-4">
            <div class="flex items-start gap-3">
              <MapPin class="w-5 h-5 text-primary mt-1" />
              <div>
                <h3 class="font-medium text-white mb-1">Store Location</h3>
                <p class="text-gray-400">{receiptData.storeAddress}</p>
                <p class="text-sm text-gray-500">{receiptData.province}</p>
              </div>
            </div>
          </div>
  
          <!-- Items List -->
          <div class="space-y-4">
            <h3 class="text-lg font-medium text-white">Items</h3>
            
            {#each Object.entries(receiptData.items) as [key, item]}
              <div class="bg-dark/30 rounded-lg p-4 backdrop-blur-sm">
                <div class="flex justify-between items-start mb-2">
                  <div class="flex-grow">
                    <h4 class="font-medium text-white">{item.itemName}</h4>
                    <div class="text-sm text-gray-400">Item #{item.itemNumber}</div>
                  </div>
                  <div class="text-right">
                    {#if item.discountApplied}
                      <div class="text-sm text-gray-500 line-through">
                        {formatPrice(item.priceBeforeDiscount)}
                      </div>
                      <div class="text-lg font-bold text-primary">
                        {formatPrice(item.priceAfterDiscount)}
                      </div>
                    {:else}
                      <div class="text-lg font-bold text-white">
                        {formatPrice(item.priceBeforeDiscount)}
                      </div>
                    {/if}
                  </div>
                </div>
                
                {#if item.discountApplied}
                  <div class="flex items-center gap-2 bg-green-900/20 border border-green-500/30 p-2 rounded-lg">
                    <Tag class="w-4 h-4 text-green-400" />
                    <span class="text-green-400 text-sm">
                      Saved {formatPrice(item.discount)}
                    </span>
                  </div>
                {/if}
              </div>
            {/each}
          </div>
        {/if}
      </div>
    </div>
  </Modal>