<script lang="ts">
  import { Menu, List } from 'lucide-svelte';
  import Logo from './nav/Logo.svelte';
  import Button from './ui/Button.svelte';
  import AuthModal from './auth/AuthModal.svelte';

  import { user, signOut } from '../stores/auth';
  import { page } from '../stores/navigation';
  
  let isMenuOpen = false;
  let isListMenuOpen = false;
  let showAuthModal = false;
  
  function handleAuthClick() {
    if (!$user) {
      showAuthModal = true;
    }
  }

  function goToPage(pageName: string) {
    if ((pageName === 'price-history') && !$user) {
      showAuthModal = true;
      return;
    }
    page.set(pageName);
    isMenuOpen = false;
    isListMenuOpen = false;
  }

  function handleClickOutside(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target.closest('.list-menu')) {
      isListMenuOpen = false;
    }
  }
</script>

<svelte:window on:click={handleClickOutside} />

<!-- Only show navbar for non-authenticated users -->
{#if !$user}
<nav class="fixed w-full bg-dark-navbar backdrop-blur-xl z-40 border-b border-gray-800/50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between h-16">
      <div class="flex-shrink-0 flex items-center">
        <Logo />
      </div>

      <div class="hidden sm:flex sm:items-center sm:space-x-6">
        <button
          class="text-gray-300 hover:text-white transition-colors flex items-center gap-2"
          on:click={() => goToPage('community')}
        >
          Community
          <span class="bg-blue-500 text-white text-xs px-2 py-0.5 rounded-full font-medium">BETA</span>
        </button>
        <button
          class="text-gray-300 hover:text-white transition-colors"
          on:click={() => goToPage('feed')}
        >
          Price Drops
        </button>
        <button
          class="text-gray-300 hover:text-white transition-colors"
          on:click={() => goToPage('flyers')}
        >
          Flyers
        </button>
        <Button on:click={() => goToPage('auth')}>
          Get Started
        </Button>
        
        <div class="relative list-menu">
          <button
            class="text-gray-300 hover:text-white transition-colors duration-200 flex items-center"
            on:click={() => isListMenuOpen = !isListMenuOpen}
            aria-label="Menu"
          >
            <List class="h-5 w-5" />
          </button>
          
          {#if isListMenuOpen}
            <div class="absolute right-0 mt-2 w-48 rounded-lg shadow-lg bg-dark-lighter border border-gray-700">
              <div class="py-1" role="menu">
                <button
                  class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-dark transition-colors"
                  on:click={() => goToPage('blog')}
                >
                  Blog
                </button>
                <button
                  class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-dark transition-colors"
                  on:click={() => goToPage('faq')}
                >
                  FAQ
                </button>
                <button
                  class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-dark transition-colors"
                  on:click={() => goToPage('about')}
                >
                  About
                </button>
              </div>
            </div>
          {/if}
        </div>
      </div>
      
      <div class="flex items-center sm:hidden">
        <button
          type="button"
          class="text-gray-400 hover:text-white"
          on:click={() => isMenuOpen = !isMenuOpen}
          aria-label="Toggle menu"
        >
          <Menu class="h-6 w-6" />
        </button>
      </div>
    </div>
    
    {#if isMenuOpen}
      <div class="sm:hidden">
        <div class="pt-2 pb-3 space-y-1">
          {#if $user}
            <div class="px-4 py-3 border-b border-gray-700">
              <p class="text-sm text-gray-400">Signed in as:</p>
              <p class="text-sm font-medium text-gray-200 truncate">{$user.email}</p>
            </div>
          {/if}
          <button
            class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-dark-lighter flex items-center justify-between"
            on:click={() => goToPage('community')}
          >
            Community
            <span class="bg-blue-500 text-white text-xs px-2 py-0.5 rounded-full font-medium">BETA</span>
          </button>
          <a
            href="/feed"
            class="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-dark-lighter"
            on:click={(e) => { e.preventDefault(); goToPage('feed'); }}
          >
            Price Drops
          </a>
          <a
            href="/flyers"
            class="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-dark-lighter"
            on:click={(e) => { e.preventDefault(); goToPage('flyers'); }}
          >
            Flyers
          </a>
          {#if $user}
            <a
              href="/price-history"
              class="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-dark-lighter"
              on:click={(e) => { e.preventDefault(); goToPage('price-history'); }}
            >
              Price History
            </a>
          {/if}
          <a
            href="/blog"
            class="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-dark-lighter"
            on:click={(e) => { e.preventDefault(); goToPage('blog'); }}
          >
            Blog
          </a>
          <a
            href="/faq"
            class="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-dark-lighter"
            on:click={(e) => { e.preventDefault(); goToPage('faq'); }}
          >
            FAQ
          </a>
          <button
            class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-dark-lighter"
            on:click={() => goToPage('about')}
          >
            About
          </button>
          {#if $user}
            <button
              class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-dark-lighter"
              on:click={() => {
                isMenuOpen = false;
                page.set('dashboard');
              }}
            >
              Dashboard
            </button>
            <button
              class="w-full text-left px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-dark-lighter"
              on:click={() => {
                isMenuOpen = false;
                signOut();
              }}
            >
              Sign out
            </button>
          {:else}
            <Button
              customClass="w-full justify-center mt-4"
              on:click={() => {
                isMenuOpen = false;
                goToPage('auth');
              }}
            >
              Get Started
            </Button>
          {/if}
        </div>
      </div>
    {/if}
  </div>
</nav>
{/if}

<AuthModal
  show={showAuthModal}
  on:close={() => showAuthModal = false}
/>