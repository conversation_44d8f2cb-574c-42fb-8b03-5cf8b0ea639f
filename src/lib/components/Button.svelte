<script lang="ts">
  import { buttonTransition } from '../utils/animation';
  
  export let type: "button" | "submit" = "button";
  export let variant: "primary" | "secondary" = "primary";
  export let className = "";
  export let disabled = false;

  const styles = {
    ...buttonTransition,
    primary: 'text-white bg-primary hover:bg-secondary disabled:bg-blue-300',
    secondary: 'text-primary bg-white hover:bg-gray-50 border-primary disabled:bg-gray-100'
  };
</script>

<button
  {type}
  {disabled}
  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md
    {styles[variant]}
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary
    transition-colors duration-200 disabled:cursor-not-allowed {className}"
  on:click
>
  <slot />
</button>