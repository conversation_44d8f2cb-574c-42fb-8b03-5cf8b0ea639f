<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Modal from './ui/Modal.svelte';
  import Button from './ui/Button.svelte';
  import { fade } from 'svelte/transition';
  
  export let show = false;
  const dispatch = createEventDispatcher();
</script>

<Modal {show} on:click={() => dispatch('close')}>
  <div class="p-8 text-center relative overflow-hidden">
    <div class="balloon-container absolute inset-0 overflow-hidden pointer-events-none">
      {#each Array(10) as _, i}
        <div class="balloon balloon-{i + 1}" />
      {/each}
    </div>
    
    <h2 class="text-2xl font-bold text-white mb-4">🎉 Success!</h2>
    <p class="text-gray-300 mb-6">
      Your product has been added successfully. We'll notify you through email if there are any price drops below your target price.
    </p>
    <Button on:click={() => dispatch('close')} fullWidth>Got it!</Button>
  </div>
</Modal>

<style>
  .balloon-container {
    height: 100%;
    width: 100%;
  }

  .balloon {
    position: absolute;
    width: 30px;
    height: 40px;
    background-color: var(--color);
    border-radius: 50%;
    animation: float var(--duration) ease-in-out infinite;
    bottom: -20px;
    opacity: 0.8;
  }

  .balloon::before {
    content: '';
    position: absolute;
    width: 4px;
    height: 10px;
    background-color: var(--color);
    bottom: -8px;
    left: 13px;
  }

  .balloon-1 { --color: #0EA5E9; --duration: 4s; left: 10%; animation-delay: 0s; }
  .balloon-2 { --color: #0284C7; --duration: 4.5s; left: 20%; animation-delay: 0.5s; }
  .balloon-3 { --color: #38BDF8; --duration: 3.5s; left: 30%; animation-delay: 1s; }
  .balloon-4 { --color: #0EA5E9; --duration: 4.2s; left: 40%; animation-delay: 1.5s; }
  .balloon-5 { --color: #0284C7; --duration: 3.8s; left: 50%; animation-delay: 2s; }
  .balloon-6 { --color: #38BDF8; --duration: 4.3s; left: 60%; animation-delay: 2.5s; }
  .balloon-7 { --color: #0EA5E9; --duration: 3.7s; left: 70%; animation-delay: 3s; }
  .balloon-8 { --color: #0284C7; --duration: 4.1s; left: 80%; animation-delay: 3.5s; }
  .balloon-9 { --color: #38BDF8; --duration: 3.9s; left: 90%; animation-delay: 4s; }
  .balloon-10 { --color: #0EA5E9; --duration: 4.4s; left: 95%; animation-delay: 4.5s; }

  @keyframes float {
    0%, 100% {
      transform: translateY(0) rotate(0deg);
    }
    50% {
      transform: translateY(-150px) rotate(8deg);
    }
  }
</style>