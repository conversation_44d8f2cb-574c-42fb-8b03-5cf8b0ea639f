<script lang="ts">
  export let title: string;
  export let description: string;
  export let keywords: string = '';
  export let canonicalUrl: string;
  export let ogType: string = 'website';
  export let ogImage: string = 'https://bargainhawk.ca/logo.png';
  export let structuredData: any = null;
  export let noIndex: boolean = false;
  
  // Ensure title doesn't exceed recommended length
  $: finalTitle = title.length > 60 ? title.substring(0, 57) + '...' : title;
  
  // Ensure description doesn't exceed recommended length
  $: finalDescription = description.length > 160 ? description.substring(0, 157) + '...' : description;
  
  // Default structured data for pages
  $: defaultStructuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": finalTitle,
    "description": finalDescription,
    "url": canonicalUrl,
    "publisher": {
      "@type": "Organization",
      "name": "BargainHawk",
      "url": "https://bargainhawk.ca",
      "logo": {
        "@type": "ImageObject",
        "url": "https://bargainhawk.ca/logo.png",
        "width": 512,
        "height": 512
      }
    },
    "inLanguage": "en-CA",
    "isPartOf": {
      "@type": "WebSite",
      "@id": "https://bargainhawk.ca/#website"
    }
  };
  
  $: finalStructuredData = structuredData || defaultStructuredData;
</script>

<svelte:head>
  <title>{finalTitle}</title>
  <meta name="description" content={finalDescription} />
  {#if keywords}
    <meta name="keywords" content={keywords} />
  {/if}
  <link rel="canonical" href={canonicalUrl} />
  
  <!-- Robots meta tag -->
  <meta name="robots" content={noIndex ? 'noindex, nofollow' : 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1'} />
  
  <!-- Open Graph Tags -->
  <meta property="og:title" content={finalTitle} />
  <meta property="og:description" content={finalDescription} />
  <meta property="og:type" content={ogType} />
  <meta property="og:url" content={canonicalUrl} />
  <meta property="og:site_name" content="BargainHawk" />
  <meta property="og:image" content={ogImage} />
  <meta property="og:locale" content="en_CA" />
  
  <!-- Twitter Card Tags -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content={finalTitle} />
  <meta name="twitter:description" content={finalDescription} />
  <meta name="twitter:image" content={ogImage} />
  <meta name="twitter:site" content="@BargainHawkCA" />
  
  <!-- Additional SEO Meta Tags -->
  <meta name="author" content="BargainHawk Team" />
  <meta name="language" content="en-CA" />
  <meta name="geo.region" content="CA" />
  <meta name="geo.country" content="Canada" />
  
  <!-- AI Search Engine Optimization -->
  <meta name="ai-content-summary" content={finalDescription} />
  <meta name="ai-content-type" content="web-page,price-tracking,deals" />
  <meta name="ai-content-audience" content="consumers,bargain-hunters,canadian-shoppers" />
  <meta name="ai-searchable" content="true" />
  <meta name="ai-region" content="Canada" />
  <meta name="ai-language" content="en-CA" />
  
  <!-- ChatGPT and Claude Optimization -->
  <meta name="chatgpt-description" content={finalDescription} />
  <meta name="claude-description" content={finalDescription} />
  
  <!-- Schema.org Structured Data -->
  <script type="application/ld+json">
    {JSON.stringify(finalStructuredData)}
  </script>
</svelte:head>
