<script lang="ts">
  import SEOLink from '../SEOLink.svelte';

  export let showTitle = true;
  export let compact = false;

  // Featured blog posts that are currently orphaned
  const featuredArticles = [
    {
      href: '/blog/costco-price-adjustment-hack',
      title: 'Costco Price Adjustment Hack',
      description: 'Complete guide to claiming Costco price adjustments and maximizing savings',
      category: 'Guides'
    },
    {
      href: '/blog/how-to-access-costco-receipts',
      title: 'How to Access Costco Receipts',
      description: 'Step-by-step guide to finding and downloading your Costco receipts online',
      category: 'Guides'
    },
    {
      href: '/blog/price-tracking-app-guide',
      title: 'Price Tracking App Guide',
      description: 'Comprehensive review of the best price tracking apps for Canadian shoppers',
      category: 'Tools'
    },
    {
      href: '/blog/best-deals-today',
      title: 'Best Deals Today',
      description: 'Daily updated list of the hottest deals and savings opportunities',
      category: 'Deals'
    },
    {
      href: '/blog/money-saving-apps-canada',
      title: 'Money Saving Apps Canada',
      description: 'Top money-saving apps every Canadian should have on their phone',
      category: 'Tools'
    },
    {
      href: '/blog/walmart-price-adjustment-guide',
      title: 'Walmart Price Adjustment Guide',
      description: 'How to get price adjustments at Walmart and save money on purchases',
      category: 'Guides'
    },
    {
      href: '/blog/costco-deals-canada',
      title: 'Costco Deals Canada',
      description: 'Best Costco deals and insider tips for Canadian members',
      category: 'Deals'
    },
    {
      href: '/blog/walmart-deals-canada',
      title: 'Walmart Deals Canada',
      description: 'Top Walmart deals and savings strategies for Canadian shoppers',
      category: 'Deals'
    },
    {
      href: '/blog/price-comparison-canada',
      title: 'Price Comparison Canada',
      description: 'Ultimate guide to price comparison tools and strategies in Canada',
      category: 'Tools'
    }
  ];
  
  // Group articles by category
  const articlesByCategory = featuredArticles.reduce((acc, article) => {
    if (!acc[article.category]) {
      acc[article.category] = [];
    }
    acc[article.category].push(article);
    return acc;
  }, {} as Record<string, typeof featuredArticles>);
</script>

{#if compact}
  <!-- Compact version for sidebars -->
  <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
    {#if showTitle}
      <h4 class="font-semibold text-gray-900 mb-3">📚 Popular Guides</h4>
    {/if}

    <div class="space-y-2">
      {#each featuredArticles.slice(0, 6) as article}
        <SEOLink
          href={article.href}
          customClass="text-primary hover:text-primary-dark text-sm block leading-tight"
        >
          {article.title}
        </SEOLink>
      {/each}
    </div>

    <div class="mt-3 pt-3 border-t border-gray-200">
      <SEOLink
        href="/blog"
        customClass="text-xs text-gray-600 hover:text-primary"
      >
        View all articles →
      </SEOLink>
    </div>
  </div>
{:else}
  <!-- Full version for main content areas -->
  <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200 shadow-sm">
    {#if showTitle}
      <h3 class="text-2xl font-bold text-gray-900 mb-2 text-center">
        💡 Popular Money-Saving Guides
      </h3>
      <p class="text-gray-600 text-center mb-6">
        Expert guides to help you save money and never miss a deal
      </p>
    {/if}
  
  <div class="grid md:grid-cols-3 gap-6">
    {#each Object.entries(articlesByCategory) as [category, articles]}
      <div class="bg-white rounded-lg p-4 shadow-md border border-gray-200 hover:shadow-lg transition-shadow">
        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
          {#if category === 'Guides'}
            📚
          {:else if category === 'Tools'}
            🛠️
          {:else if category === 'Deals'}
            💰
          {/if}
          <span class="ml-2">{category}</span>
        </h4>
        
        <div class="space-y-3">
          {#each articles as article}
            <div class="border-l-2 border-primary/20 pl-3">
              <SEOLink 
                href={article.href} 
                customClass="text-primary hover:text-primary-dark font-medium text-sm block leading-tight"
              >
                {article.title}
              </SEOLink>
              <p class="text-gray-500 text-xs mt-1 leading-relaxed">
                {article.description}
              </p>
            </div>
          {/each}
        </div>
      </div>
    {/each}
  </div>
  
  <!-- Call to action -->
  <div class="text-center mt-6 pt-4 border-t border-blue-200">
    <p class="text-gray-600 text-sm mb-3">
      Want more money-saving tips and deal alerts?
    </p>
    <div class="flex flex-wrap justify-center gap-3">
      <SEOLink
        href="/blog"
        customClass="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors text-sm font-medium shadow-sm"
      >
        Browse All Articles
      </SEOLink>
      <SEOLink
        href="/feed"
        customClass="border border-primary text-primary px-4 py-2 rounded-lg hover:bg-primary/10 transition-colors text-sm font-medium"
      >
        View Live Price Drops
      </SEOLink>
      <SEOLink
        href="/auth"
        customClass="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors text-sm font-medium"
      >
        Start Tracking Prices
      </SEOLink>
    </div>
  </div>
  </div>
{/if}
