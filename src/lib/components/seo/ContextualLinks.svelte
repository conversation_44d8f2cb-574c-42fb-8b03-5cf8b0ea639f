<script lang="ts">
  export let pageType: 'feed' | 'blog' | 'community' | 'flyers' | 'about' | 'terms' | 'privacy' | 'general' = 'general';
  export let customLinks: Array<{href: string, text: string, description?: string}> = [];
  
  import SEOLink from '../SEOLink.svelte';
  
  // Define contextual links based on page type
  const linkSets = {
    feed: [
      { href: '/blog/best-deals-today', text: 'Best Deals Today', description: 'Discover today\'s top savings' },
      { href: '/blog/costco-price-adjustment-hack', text: 'Costco Price Adjustments', description: 'Learn how to claim refunds' },
      { href: '/blog/walmart-price-adjustment-guide', text: 'Walmart Price Guide', description: 'Walmart savings strategies' },
      { href: '/community', text: 'Join Community', description: 'Share deals with other savers' },
      { href: '/auth', text: 'Track Your Items', description: 'Start saving with free account' },
      { href: '/flyers', text: 'Store Flyers', description: 'Browse weekly deals' }
    ],
    blog: [
      { href: '/feed', text: 'Latest Price Drops', description: 'See current deals and savings' },
      { href: '/blog', text: 'All Guides', description: 'Browse money-saving articles' },
      { href: '/community', text: 'Community Deals', description: 'User-shared savings tips' },
      { href: '/auth', text: 'Start Tracking', description: 'Create free account' },
      { href: '/faq', text: 'FAQ', description: 'Common questions answered' }
    ],
    community: [
      { href: '/feed', text: 'Price Drops Feed', description: 'Real-time price alerts' },
      { href: '/blog/best-deals-today', text: 'Today\'s Best Deals', description: 'Curated daily savings' },
      { href: '/blog', text: 'Savings Guides', description: 'Expert money-saving tips' },
      { href: '/auth', text: 'Join BargainHawk', description: 'Free price tracking tools' },
      { href: '/flyers', text: 'Store Flyers', description: 'Weekly store promotions' }
    ],
    flyers: [
      { href: '/feed', text: 'Live Price Drops', description: 'Current deals and discounts' },
      { href: '/blog/costco-deals-canada', text: 'Costco Deals Guide', description: 'Maximize Costco savings' },
      { href: '/blog/walmart-deals-canada', text: 'Walmart Deals Guide', description: 'Best Walmart strategies' },
      { href: '/community', text: 'Deal Community', description: 'Share and discover deals' },
      { href: '/auth', text: 'Track Prices', description: 'Automated price monitoring' }
    ],
    about: [
      { href: '/feed', text: 'See Price Drops', description: 'Browse current savings' },
      { href: '/blog', text: 'Money-Saving Guides', description: 'Learn saving strategies' },
      { href: '/auth', text: 'Get Started Free', description: 'Start tracking prices' },
      { href: '/faq', text: 'FAQ', description: 'Common questions' },
      { href: '/community', text: 'Join Community', description: 'Connect with savers' }
    ],
    terms: [
      { href: '/', text: 'Homepage', description: 'Return to main site' },
      { href: '/privacy', text: 'Privacy Policy', description: 'How we protect your data' },
      { href: '/faq', text: 'FAQ', description: 'Frequently asked questions' },
      { href: '/about', text: 'About BargainHawk', description: 'Learn about our mission' },
      { href: '/auth', text: 'Create Account', description: 'Start saving money today' }
    ],
    privacy: [
      { href: '/', text: 'Homepage', description: 'Return to main site' },
      { href: '/terms', text: 'Terms of Service', description: 'Service terms and conditions' },
      { href: '/faq', text: 'FAQ', description: 'Common questions answered' },
      { href: '/about', text: 'About Us', description: 'Our company information' },
      { href: '/auth', text: 'Sign Up', description: 'Join BargainHawk today' }
    ],
    general: [
      { href: '/feed', text: 'Price Drops', description: 'Latest deals and savings' },
      { href: '/blog', text: 'Savings Guides', description: 'Money-saving articles' },
      { href: '/community', text: 'Community', description: 'Share deals and tips' },
      { href: '/auth', text: 'Get Started', description: 'Free price tracking' },
      { href: '/faq', text: 'FAQ', description: 'Get help and answers' }
    ]
  };
  
  $: links = customLinks.length > 0 ? customLinks : linkSets[pageType];
</script>

<div class="mt-12 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200 shadow-sm">
  <h3 class="text-xl font-semibold text-gray-900 mb-4 text-center">
    💡 Explore More Ways to Save
  </h3>
  <p class="text-gray-600 text-center mb-6">
    Discover additional resources to maximize your savings and never miss a deal.
  </p>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    {#each links as link}
      <div class="bg-white rounded-lg p-4 hover:bg-blue-50 transition-colors border border-gray-200 shadow-sm">
        <SEOLink href={link.href} customClass="text-primary hover:text-primary-dark font-medium text-lg">
          {link.text}
        </SEOLink>
        {#if link.description}
          <p class="text-gray-600 text-sm mt-1">{link.description}</p>
        {/if}
      </div>
    {/each}
  </div>
  
  <!-- Additional CTA -->
  <div class="text-center mt-6 pt-4 border-t border-gray-700">
    <p class="text-gray-400 text-sm">
      New to BargainHawk? 
      <SEOLink href="/auth" customClass="text-primary hover:text-primary-light font-medium">
        Start tracking prices for free
      </SEOLink>
      and join thousands of smart shoppers saving money every day.
    </p>
  </div>
</div>
