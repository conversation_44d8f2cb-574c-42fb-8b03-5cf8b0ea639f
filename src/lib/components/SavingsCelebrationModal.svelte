<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { Trophy, DollarSign, TrendingUp, X, Sparkles } from 'lucide-svelte';
  import SocialShare from './SocialShare.svelte';
  import Button from './ui/Button.svelte';
  
  export let isOpen = false;
  export let savings = '$0';
  export let productName = '';
  export let originalPrice = '$0';
  export let salePrice = '$0';
  export let store = '';
  
  const dispatch = createEventDispatcher();
  
  let confettiContainer: HTMLDivElement;
  
  $: if (isOpen && confettiContainer) {
    // Trigger confetti animation
    createConfetti();
  }
  
  function close() {
    isOpen = false;
    dispatch('close');
  }
  
  function createConfetti() {
    // Simple confetti effect
    for (let i = 0; i < 50; i++) {
      setTimeout(() => {
        const confetti = document.createElement('div');
        confetti.className = 'confetti-piece';
        confetti.style.left = Math.random() * 100 + '%';
        confetti.style.animationDelay = Math.random() * 3 + 's';
        confetti.style.backgroundColor = getRandomColor();
        confettiContainer?.appendChild(confetti);
        
        // Remove confetti after animation
        setTimeout(() => {
          confetti.remove();
        }, 4000);
      }, i * 50);
    }
  }
  
  function getRandomColor() {
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff'];
    return colors[Math.floor(Math.random() * colors.length)];
  }
  
  $: shareTitle = `🎉 I just saved ${savings} on ${productName}!`;
  $: shareDescription = `Found this amazing deal using BargainHawk's price tracking. Was ${originalPrice}, got it for ${salePrice} at ${store}. Never paying full price again! 💰`;
  $: shareHashtags = ['BargainHawk', 'DealsCanada', 'MoneySaving', 'PriceTracking', store.toLowerCase()];
</script>

{#if isOpen}
  <!-- Modal Backdrop -->
  <div class="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
    <!-- Confetti Container -->
    <div bind:this={confettiContainer} class="fixed inset-0 pointer-events-none overflow-hidden"></div>
    
    <!-- Modal Content -->
    <div class="bg-gradient-to-br from-dark-lighter to-dark border border-gray-700 rounded-2xl p-8 max-w-md w-full relative overflow-hidden">
      <!-- Close Button -->
      <button
        class="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
        on:click={close}
        aria-label="Close"
      >
        <X class="h-6 w-6" />
      </button>
      
      <!-- Success Animation Background -->
      <div class="absolute inset-0 opacity-10">
        <div class="absolute top-4 left-4 animate-bounce">
          <Sparkles class="h-8 w-8 text-primary" />
        </div>
        <div class="absolute top-8 right-8 animate-bounce delay-300">
          <Trophy class="h-6 w-6 text-yellow-400" />
        </div>
        <div class="absolute bottom-8 left-8 animate-bounce delay-500">
          <DollarSign class="h-7 w-7 text-green-400" />
        </div>
      </div>
      
      <!-- Main Content -->
      <div class="text-center relative z-10">
        <!-- Trophy Icon -->
        <div class="mx-auto w-20 h-20 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mb-6 animate-pulse">
          <Trophy class="h-10 w-10 text-white" />
        </div>
        
        <!-- Celebration Text -->
        <h2 class="text-3xl font-bold text-white mb-2">
          🎉 Congratulations!
        </h2>
        <p class="text-xl text-primary-light mb-6">
          You just saved <span class="text-green-400 font-bold text-2xl">{savings}</span>!
        </p>
        
        <!-- Deal Details -->
        <div class="bg-dark/50 rounded-lg p-4 mb-6 text-left">
          <div class="text-sm text-gray-400 mb-2">Your winning deal:</div>
          <div class="text-white font-medium mb-1">{productName}</div>
          <div class="flex justify-between items-center text-sm">
            <span class="text-gray-400">
              <span class="line-through">{originalPrice}</span> → <span class="text-green-400 font-bold">{salePrice}</span>
            </span>
            <span class="px-2 py-1 bg-primary/20 text-primary rounded text-xs">
              {store.toUpperCase()}
            </span>
          </div>
        </div>
        
        <!-- Stats -->
        <div class="grid grid-cols-2 gap-4 mb-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-primary">
              {Math.round((parseFloat(savings.replace('$', '')) / parseFloat(originalPrice.replace('$', ''))) * 100)}%
            </div>
            <div class="text-xs text-gray-400">Discount</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-400">
              <TrendingUp class="h-6 w-6 inline" />
            </div>
            <div class="text-xs text-gray-400">Smart Shopping</div>
          </div>
        </div>
        
        <!-- Social Share -->
        <div class="mb-6">
          <p class="text-sm text-gray-300 mb-3">
            Share your success and help friends save money too! 🚀
          </p>
          <SocialShare 
            title={shareTitle}
            description={shareDescription}
            hashtags={shareHashtags}
            savings={savings}
          />
        </div>
        
        <!-- Action Buttons -->
        <div class="space-y-3">
          <Button 
            on:click={close}
            class="w-full bg-primary hover:bg-primary-dark"
          >
            Continue Shopping
          </Button>
          <button
            class="w-full text-sm text-gray-400 hover:text-white transition-colors"
            on:click={close}
          >
            Track more deals →
          </button>
        </div>
        
        <!-- Motivational Message -->
        <div class="mt-6 p-3 bg-gradient-to-r from-primary/10 to-primary-dark/10 rounded-lg">
          <p class="text-xs text-gray-300">
            💡 <strong>Keep it up!</strong> Smart shoppers like you save an average of $500+ annually with BargainHawk.
          </p>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  :global(.confetti-piece) {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #ff6b6b;
    animation: confetti-fall 4s linear forwards;
    opacity: 0.8;
  }
  
  @keyframes confetti-fall {
    0% {
      transform: translateY(-100vh) rotate(0deg);
      opacity: 1;
    }
    100% {
      transform: translateY(100vh) rotate(720deg);
      opacity: 0;
    }
  }
</style>
