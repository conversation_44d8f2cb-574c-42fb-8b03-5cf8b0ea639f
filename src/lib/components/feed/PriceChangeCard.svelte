<script lang="ts">
  import type { PriceChange } from '../../types/priceChange';
  import { formatPrice, formatTimeAgo } from '../../utils/format';
  import { ExternalLink, TrendingDown, Clock } from 'lucide-svelte';
  import { fade, fly } from 'svelte/transition';
  
  export let priceChange: PriceChange;
  
  let isHovered = false;
  let imageError = false;

  function handleImageError() {
    imageError = true;
  }

  function handleClick() {
    window.open(priceChange.url, '_blank', 'noopener,noreferrer');
  }

  function handleMouseEnter() {
    isHovered = true;
  }

  function handleMouseLeave() {
    isHovered = false;
  }

  $: priceDifference = priceChange.previousPrice - priceChange.newPrice;
  $: percentageChange = (priceDifference / priceChange.previousPrice) * 100;
</script>

<button
  class="block w-full text-left bg-dark-lighter/50 rounded-lg border border-gray-800/50 backdrop-blur-sm
         hover:border-gray-700 transition-all duration-300 h-full flex flex-col cursor-pointer
         overflow-hidden"
  on:mouseenter={handleMouseEnter}
  on:mouseleave={handleMouseLeave}
  on:click={handleClick}
  in:fly={{ y: 20, duration: 300 }}
>
  <div class="relative aspect-square w-full sm:h-[250px] sm:aspect-auto overflow-hidden bg-dark/50 flex-shrink-0">
    {#if !imageError}
      <img
        src={priceChange.imageUrl}
        alt="Product"
        class="w-full h-full object-contain transform transition-transform duration-300 {isHovered ? 'scale-105' : ''}"
        on:error={handleImageError}
      />
    {:else}
      <div class="w-full h-full flex items-center justify-center text-gray-500">
        <span class="text-sm">No image available</span>
      </div>
    {/if}
    
    {#if isHovered}
      <div 
        class="absolute inset-0 bg-dark/80 backdrop-blur-sm flex items-center justify-center"
        transition:fade={{ duration: 200 }}
      >
        <ExternalLink class="w-8 h-8 text-gray-400" />
      </div>
    {/if}
  </div>

  <div class="p-4 flex-grow flex flex-col">
    <!-- Date component -->
    <div class="flex items-center gap-1 text-xs text-gray-500 mb-3">
      <Clock class="w-3 h-3" />
      <span>{formatTimeAgo(priceChange.createdDate)}</span>
    </div>

    <div class="mt-auto space-y-3">
      <div class="flex items-baseline justify-between">
        <span class="text-sm text-gray-400">Price Before:</span>
        <span class="text-gray-500 line-through">
          {formatPrice(priceChange.previousPrice)}
        </span>
      </div>

      <div class="flex items-baseline justify-between">
        <span class="text-sm text-gray-400">New Price:</span>
        <span class="text-lg font-bold text-primary">
          {formatPrice(priceChange.newPrice)}
        </span>
      </div>

      <!-- Price drop indicator (simplified since we only show drops) -->
      <div class="bg-green-900/20 border-green-500/30 border p-3 rounded-lg text-center backdrop-blur-sm flex items-center justify-center gap-2">
        <TrendingDown class="w-4 h-4 text-green-400" />
        <span class="text-green-400 font-medium text-sm">
          Save {formatPrice(priceDifference)} ({percentageChange.toFixed(1)}%)
        </span>
      </div>
    </div>
  </div>
</button>