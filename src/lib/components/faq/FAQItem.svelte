<script lang="ts">
    import type { FAQ } from '../../data/faqData';
    import { slide } from 'svelte/transition';
    
    export let faq: FAQ;
    let isOpen = false;
  </script>
  
  <div class="bg-white bg-opacity-10 rounded-lg backdrop-blur-sm">
    <button
      class="w-full text-left px-6 py-4 focus:outline-none focus:ring-2 focus:ring-blue-400 rounded-lg"
      on:click={() => isOpen = !isOpen}
      aria-expanded={isOpen}
    >
      <h3 class="text-xl font-semibold text-blue-200 flex justify-between items-center">
        {faq.question}
        <svg
          class="w-6 h-6 transform transition-transform duration-200 {isOpen ? 'rotate-180' : ''}"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </h3>
    </button>
    
    {#if isOpen}
      <div class="px-6 pb-4" transition:slide>
        <p class="text-blue-100">
          {faq.answer}
        </p>
      </div>
    {/if}
  </div>