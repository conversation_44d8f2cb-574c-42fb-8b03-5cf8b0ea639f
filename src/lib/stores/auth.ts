import { writable } from 'svelte/store';
import { supabase } from '../utils/supabase';
import type { User } from '@supabase/supabase-js';
import { page } from './navigation';

export const user = writable<User | null>(null);
export const authError = writable<string | null>(null);

// Initialize auth state
supabase.auth.onAuthStateChange((event, session) => {
  const currentUser = session?.user ?? null;
  user.set(currentUser);
  
  // Only navigate on explicit sign in/out events, not on session refresh
  if (event === 'SIGNED_IN') {
    authError.set(null);
    // Don't automatically redirect to dashboard - let users stay on current page
  } else if (event === 'SIGNED_OUT') {
    authError.set(null);
    page.set('home');
  }
});

export async function signIn(email: string, password: string) {
  try {
    authError.set(null);
    const { data, error } = await supabase.auth.signInWithPassword({ 
      email, 
      password 
    });
    
    if (error) {
      if (error.message === 'Invalid login credentials') {
        throw new Error('Invalid email or password. Please try again.');
      }
      throw error;
    }
    
    return data;
  } catch (e) {
    const message = e instanceof Error ? e.message : 'Failed to sign in';
    authError.set(message);
    throw e;
  }
}

export async function signUp(email: string, password: string) {
  try {
    authError.set(null);
    const { data, error } = await supabase.auth.signUp({ 
      email, 
      password,
      options: {
        emailRedirectTo: window.location.origin
      }
    });
    
    if (error) throw error;
    return data;
  } catch (e) {
    const message = e instanceof Error ? e.message : 'Failed to create account';
    authError.set(message);
    throw e;
  }
}

export async function signOut() {
  try {
    authError.set(null);

    // Check if we have a valid session first
    const { data: { session } } = await supabase.auth.getSession();

    if (session) {
      // We have a valid session, try normal logout
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } else {
      // No valid session, just clear local state manually
      console.log('No valid session found, clearing local state');
      // Manually clear the local storage
      localStorage.removeItem('sb-tovaxwkyjnrfssqzpmcl-auth-token');
      // Clear user state
      user.set(null);
      page.set('home');
    }

  } catch (e) {
    // If any error occurs, force clear local state
    console.log('Logout error, forcing local cleanup:', e);

    // Manually clear local storage
    try {
      localStorage.removeItem('sb-tovaxwkyjnrfssqzpmcl-auth-token');
    } catch (storageError) {
      console.log('Could not clear localStorage:', storageError);
    }

    // Clear user state
    user.set(null);
    page.set('home');

    // Don't throw the error - we've cleaned up locally
    return;
  }
}