import { writable } from 'svelte/store';

export type Page = 'dashboard' | 'about' | 'faq' | 'privacy' | 'terms' | 'blog' | 'feed' | 'flyers' | 'price-history' | 'reset-password' | 'forgot-password' | 'auth' | 'home' | 'community' | 'account' | 'website-tracking' | 'unsubscribe' | string;
export const page = writable<Page>('home');

function scrollToTop() {
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: 'smooth'
  });
}

function updateBrowserHistory(path: string) {
  if (window.location.pathname !== path) {
    window.history.pushState({}, '', path);
  }
}

function initializePageFromUrl() {
  const path = window.location.pathname;

  // Handle /blog routes (using new system)
  if (path.startsWith('/blog/')) {
    page.set(`blog/${path.slice(6)}`);
    return;
  }

  // Handle legacy /blogs routes (redirect to /blog)
  if (path.startsWith('/blogs/')) {
    const slug = path.slice(7);
    window.history.replaceState({}, '', `/blog/${slug}`);
    page.set(`blog/${slug}`);
    return;
  }

  // Handle community post routes
  if (path.startsWith('/community/') && path !== '/community') {
    page.set(`community/${path.slice(11)}`); // Extract post ID
    return;
  }

  // Handle flyer store routes
  if (path.startsWith('/flyers/') && path !== '/flyers') {
    page.set(`flyers/${path.slice(8)}`); // Extract store slug
    return;
  }

  const routes: Record<string, Page> = {
    '/dashboard': 'dashboard',
    '/about': 'about',
    '/faq': 'faq',
    '/privacy': 'privacy',
    '/terms': 'terms',
    '/blog': 'blog', // Main blog route using new system
    '/blogs': 'blogs', // Legacy route - will redirect to /blog
    '/feed': 'feed',
    '/flyers': 'flyers',
    '/price-history': 'price-history',
    '/website-tracking': 'website-tracking',
    '/account': 'account',
    '/community': 'community',
    '/reset-password': 'reset-password',
    '/forgot-password': 'forgot-password',
    '/auth': 'auth',
    '/unsubscribe': 'unsubscribe',
    '/': 'home'
  };

  const newPage = routes[path] || 'home';
  page.set(newPage);
}

function setupNavigationHandlers() {
  page.subscribe(currentPage => {
    let newPath: string;
    if (currentPage.startsWith('blog/')) {
      newPath = `/blog/${currentPage.slice(5)}`;
    } else if (currentPage.startsWith('blogs/')) {
      // Legacy blogs routes - redirect to new /blog path
      newPath = `/blog/${currentPage.slice(6)}`;
    } else if (currentPage.startsWith('community/')) {
      newPath = `/community/${currentPage.slice(10)}`;
    } else if (currentPage.startsWith('flyers/')) {
      newPath = `/flyers/${currentPage.slice(7)}`;
    } else {
      newPath = currentPage === 'home' ? '/' : `/${currentPage}`;
    }
    updateBrowserHistory(newPath);
    scrollToTop();
  });

  window.addEventListener('popstate', () => {
    initializePageFromUrl();
    scrollToTop();
  });
}

initializePageFromUrl();
setupNavigationHandlers();