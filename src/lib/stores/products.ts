import { get, writable } from 'svelte/store';
import { user } from './auth';
import { fetchWithAuth } from '../utils/api';
import type { Product } from '../types';

export const lastAddedProductId = writable<string | null>(null);
export const productsError = writable<string | null>(null);

export async function getProducts(): Promise<Product[]> {
  const currentUser = get(user);
  if (!currentUser?.email) {
    throw new Error('Please sign in to view your products');
  }

  try {
    productsError.set(null);
    const response = await fetchWithAuth(`productList?email=${encodeURIComponent(currentUser.email)}`);
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('Your session has expired. Please sign in again.');
      }
      throw new Error('Failed to fetch products');
    }

    const products: Product[] = await response.json();
    return products;
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Failed to load products';
    productsError.set(message);
    console.error('Error fetching products:', error);
    throw error;
  }
}

export async function addProduct(url: string, targetPrice: number, productName: string, location: string): Promise<void> {
  const currentUser = get(user);
  if (!currentUser?.email) {
    throw new Error('Please sign in to add products');
  }

  try {
    productsError.set(null);
    const response = await fetchWithAuth('fetch-url', {
      method: 'POST',
      body: JSON.stringify({
        url,
        email: currentUser.email,
        productName,
        price: targetPrice,
        province: location
      })
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('Your session has expired. Please sign in again.');
      }
      throw new Error('Failed to add product');
    }

    const data = await response.json();
    lastAddedProductId.set(data.id);
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Failed to add product';
    productsError.set(message);
    console.error('Error adding product:', error);
    throw error;
  }
}

export async function updateProduct(id: string, targetPrice: number, location: string): Promise<void> {
  try {
    productsError.set(null);
    const response = await fetchWithAuth(`products/${id}`, {
      method: 'PATCH',
      body: JSON.stringify({
        userPrice: targetPrice,
        province: location
      })
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('Your session has expired. Please sign in again.');
      }
      throw new Error('Failed to update product');
    }
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Failed to update product';
    productsError.set(message);
    console.error('Error updating product:', error);
    throw error;
  }
}

export async function deleteProduct(id: string): Promise<void> {
  try {
    productsError.set(null);
    const response = await fetchWithAuth(`products/${id}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('Your session has expired. Please sign in again.');
      }
      throw new Error('Failed to delete product');
    }
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Failed to delete product';
    productsError.set(message);
    console.error('Error deleting product:', error);
    throw error;
  }
}