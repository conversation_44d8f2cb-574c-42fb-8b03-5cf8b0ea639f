<script lang="ts">
    import { onMount } from 'svelte';
    import { page } from '../stores/navigation';
    import { supabase } from '../utils/supabase';
    import Button from '../components/ui/Button.svelte';
    import PasswordInput from '../components/auth/PasswordInput.svelte';
    import { user } from '../stores/auth';
  
    let oldPassword = '';
    let newPassword = '';
    let confirmPassword = '';
    let error = '';
    let loading = false;
    let success = false;
  
    // Redirect if user is not authenticated
    onMount(() => {
      if (!$user) {
        page.set('home');
      }
    });
  
    async function handleSubmit() {
      if (newPassword !== confirmPassword) {
        error = 'New password and confirmation do not match';
        return;
      }
  
      if (newPassword.length < 6) {
        error = 'Password must be at least 6 characters long';
        return;
      }
  
      try {
        loading = true;
        error = '';
  
        // Get current user first
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user?.email) {
          throw new Error('Unable to verify current user');
        }
  
        // Verify current password
        const { error: signInError } = await supabase.auth.signInWithPassword({
          email: user.email,
          password: oldPassword
        });
  
        if (signInError) {
          throw new Error('Current password is incorrect');
        }
  
        // Update password
        const { error: updateError } = await supabase.auth.updateUser({
          password: newPassword
        });
  
        if (updateError) throw updateError;
  
        success = true;
        setTimeout(() => {
          page.set('dashboard');
        }, 2000);
      } catch (e) {
        error = e instanceof Error ? e.message : 'Failed to update password';
      } finally {
        loading = false;
      }
    }
  </script>
  
  <svelte:head>
    <title>Change Password | BargainHawk</title>
  </svelte:head>
  
  <div class="min-h-screen gradient-bg pt-24">
    <div class="max-w-md mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-dark-lighter/50 rounded-lg border border-gray-800/50 backdrop-blur-sm p-6">
        {#if success}
          <div class="text-center">
            <h2 class="text-2xl font-bold text-white mb-4">Password Updated</h2>
            <p class="text-gray-300 mb-6">
              Your password has been successfully updated. Redirecting to dashboard...
            </p>
          </div>
        {:else}
          <h1 class="text-2xl font-bold text-white mb-6">Change Password</h1>
  
          <form on:submit|preventDefault={handleSubmit} class="space-y-4">
            <PasswordInput
              label="Current Password"
              bind:value={oldPassword}
              required
            />
  
            <PasswordInput
              label="New Password"
              bind:value={newPassword}
              required
            />
  
            <PasswordInput
              label="Confirm New Password"
              bind:value={confirmPassword}
              required
            />
  
            {#if error}
              <p class="text-red-400 text-sm">{error}</p>
            {/if}
  
            <Button type="submit" disabled={loading} fullWidth>
              {loading ? 'Updating...' : 'Update Password'}
            </Button>
          </form>
        {/if}
      </div>
    </div>
  </div>