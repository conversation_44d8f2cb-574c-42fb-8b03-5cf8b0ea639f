<script lang="ts">
    import SearchBar from '../components/priceHistory/SearchBar.svelte';
    import PriceChart from '../components/priceHistory/PriceChart.svelte';
    import AddTrackingModal from '../components/priceHistory/AddTrackingModal.svelte';
    import Button from '../components/ui/Button.svelte';
    import { getPriceHistory } from '../services/priceHistory';
    import type { PriceHistoryItem } from '../types/priceHistory';
    import { writable } from 'svelte/store';
  
    let loading = false;
    let error = '';
    let priceHistory = writable<PriceHistoryItem[]>([]);
    let showAddModal = false;
  
    async function handleSearch(event: CustomEvent<string>) {
      try {
        loading = true;
        error = '';
        const data = await getPriceHistory(event.detail);
        priceHistory.set(data);
      } catch (e) {
        error = e instanceof Error ? e.message : 'Failed to load price history';
      } finally {
        loading = false;
      }
    }
  
    function handleTrackingSuccess() {
      showAddModal = false;
    }
  </script>
  
  <svelte:head>
    <title>Price History | BargainHawk</title>
    <meta name="description" content="View historical price trends for Costco products and make informed purchasing decisions." />
  </svelte:head>
  
  <div class="min-h-screen gradient-bg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-white">Price History</h1>
        <Button on:click={() => showAddModal = true}>
          Add New Tracking
        </Button>
      </div>
      
      <SearchBar {loading} on:search={handleSearch} />
      
      {#if error}
        <div class="mt-4 p-4 bg-red-900/20 border-l-4 border-red-500 rounded-r-lg backdrop-blur-sm">
          <p class="text-red-400">{error}</p>
        </div>
      {/if}
      
      {#if loading}
        <div class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      {:else}
        <PriceChart data={$priceHistory} />
      {/if}
    </div>
  </div>
  
  <AddTrackingModal
    show={showAddModal}
    on:close={() => showAddModal = false}
    on:success={handleTrackingSuccess}
  />