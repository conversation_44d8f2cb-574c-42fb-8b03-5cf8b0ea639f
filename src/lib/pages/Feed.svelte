<script lang="ts">
  import { onMount } from 'svelte';
  import PriceChangeCard from '../components/feed/PriceChangeCard.svelte';
  import ContextualLinks from '../components/seo/ContextualLinks.svelte';
  import type { PriceChange } from '../types/priceChange';
  import { getPriceChanges } from '../services/priceChange';

  let priceChanges: PriceChange[] = [];
  let loading = true;
  let error = '';

  async function loadPriceChanges() {
    try {
      loading = true;
      error = '';
      const allPriceChanges = await getPriceChanges();
      // Filter to show only price drops (where new price is less than previous price)
      priceChanges = allPriceChanges.filter(change => change.newPrice < change.previousPrice);
    } catch (e) {
      error = e instanceof Error ? e.message : 'Failed to load price changes';
    } finally {
      loading = false;
    }
  }

  onMount(loadPriceChanges);
</script>

<svelte:head>
  <title>Live Price Drops & Deal Alerts | BargainHawk Canada</title>
  <meta name="description" content="Browse real-time price drops from Costco, Walmart, and Wayfair. Get instant notifications when prices drop on your favorite products. Never miss a deal again!" />
  <meta name="keywords" content="price drops, live deals, price alerts, costco price drops, walmart deals, wayfair sales, canadian deals, real time price tracking" />
  <link rel="canonical" href="https://bargainhawk.ca/feed" />

  <!-- Open Graph Tags -->
  <meta property="og:title" content="Live Price Drops & Deal Alerts | BargainHawk Canada" />
  <meta property="og:description" content="Browse real-time price drops from Costco, Walmart, and Wayfair. Get instant notifications when prices drop on your favorite products." />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://bargainhawk.ca/feed" />
  <meta property="og:site_name" content="BargainHawk" />
  <meta property="og:image" content="https://bargainhawk.ca/logo.png" />
</svelte:head>

<div class="min-h-screen gradient-bg pt-24">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-white mb-8">Recent Price Drops</h1>

    {#if error}
      <div class="bg-red-900/20 border-l-4 border-red-500 p-4 mb-8 rounded-r-lg backdrop-blur-sm">
        <p class="text-red-400">{error}</p>
      </div>
    {/if}

    {#if loading}
      <div class="flex justify-center items-center min-h-[200px]">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    {:else if priceChanges.length === 0}
      <div class="text-center py-12 bg-dark-lighter/50 rounded-lg border border-gray-800/50 backdrop-blur-sm">
        <h2 class="text-xl font-medium text-white mb-2">No Recent Price Drops</h2>
        <p class="text-gray-400">Check back later for new price drops!</p>
      </div>
    {:else}
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {#each priceChanges as priceChange (priceChange.id)}
          <PriceChangeCard {priceChange} />
        {/each}
      </div>
    {/if}

    <!-- Contextual links for SEO and user engagement -->
    <ContextualLinks pageType="feed" />
  </div>
</div>