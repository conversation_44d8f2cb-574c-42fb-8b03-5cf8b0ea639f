  <script lang="ts">
    import { onMount } from 'svelte';
    import { page } from '../stores/navigation';
    import { supabase } from '../utils/supabase';
    import Button from '../components/ui/Button.svelte';
    import PasswordInput from '../components/auth/PasswordInput.svelte';

    let newPassword = '';
    let confirmPassword = '';
    let error = '';
    let loading = false;
    let success = false;

    onMount(async () => {
      try {
        // Check if user is already authenticated (session was set)
        const { data: { session } } = await supabase.auth.getSession();
        if (session) {
          console.log('User already has active session, ready for password reset');
          return;
        }

        // Get the full URL including hash and search params
        const hash = window.location.hash;
        const search = window.location.search;

        console.log('Current URL:', window.location.href);
        console.log('Hash:', hash);
        console.log('Search:', search);

        let params;
        let accessToken = null;
        let refreshToken = null;
        let type = null;

        // Try to parse from hash first (most common for Supabase)
        if (hash) {
          params = new URLSearchParams(hash.substring(1));
          accessToken = params.get('access_token');
          refreshToken = params.get('refresh_token');
          type = params.get('type');
        }

        // If no tokens in hash, try search params
        if (!accessToken && search) {
          params = new URLSearchParams(search);
          accessToken = params.get('access_token');
          refreshToken = params.get('refresh_token');
          type = params.get('type');
        }

        console.log('Parsed tokens:', {
          accessToken: accessToken ? 'present' : 'missing',
          refreshToken: refreshToken ? 'present' : 'missing',
          type,
          accessTokenLength: accessToken?.length,
          refreshTokenLength: refreshToken?.length
        });

        // If we still don't have tokens, this might be a direct visit to the page
        if (!accessToken) {
          console.log('No access token found in URL');
          error = 'Invalid reset link. Please request a new one.';
          return;
        }

        if (type !== 'recovery') {
          console.log('Invalid type:', type);
          error = 'Invalid reset link. Please request a new one.';
          return;
        }

        console.log('Setting session with tokens...');

        // Set the session with the tokens
        const { error: sessionError } = await supabase.auth.setSession({
          access_token: accessToken,
          refresh_token: refreshToken || ''
        });

        if (sessionError) {
          console.error('Session error:', sessionError);
          throw sessionError;
        }

        console.log('Session set successfully');

        // Clear the URL hash and search params
        history.replaceState(null, '', window.location.pathname);
      } catch (e) {
        console.error('Error in onMount:', e);
        error = 'Invalid or expired reset link. Please request a new one.';
      }
    });

    async function handleSubmit() {
      if (newPassword !== confirmPassword) {
        error = 'Passwords do not match';
        return;
      }

      if (newPassword.length < 6) {
        error = 'Password must be at least 6 characters long';
        return;
      }

      try {
        loading = true;
        error = '';

        const { error: updateError } = await supabase.auth.updateUser({
          password: newPassword
        });

        if (updateError) throw updateError;

        success = true;
        setTimeout(() => {
          page.set('dashboard');
        }, 2000);
      } catch (e) {
        error = e instanceof Error ? e.message : 'Failed to update password';
      } finally {
        loading = false;
      }
    }
  </script>

  <svelte:head>
    <title>Reset Password | BargainHawk</title>
  </svelte:head>

  <div class="min-h-screen gradient-bg pt-24">
    <div class="max-w-md mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-dark-lighter/50 rounded-lg border border-gray-800/50 backdrop-blur-sm p-6">
        {#if success}
          <div class="text-center">
            <h2 class="text-2xl font-bold text-white mb-4">Password Updated</h2>
            <p class="text-gray-300 mb-6">
              Your password has been successfully reset. Redirecting to dashboard...
            </p>
          </div>
        {:else}
          <h1 class="text-2xl font-bold text-white mb-6">Reset Password</h1>

          <form on:submit|preventDefault={handleSubmit} class="space-y-4">
            <PasswordInput
              label="New Password"
              bind:value={newPassword}
              required
            />

            <PasswordInput
              label="Confirm New Password"
              bind:value={confirmPassword}
              required
            />

            {#if error}
              <p class="text-red-400 text-sm">{error}</p>
            {/if}

            <Button type="submit" disabled={loading || !!error} fullWidth>
              {loading ? 'Updating...' : 'Reset Password'}
            </Button>
          </form>
        {/if}
      </div>
    </div>
  </div>