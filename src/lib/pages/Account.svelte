<script lang="ts">
  import { onMount } from 'svelte';
  import { Settings, Mail, User, Save, Check, X } from 'lucide-svelte';
  import { fetchWithAuth } from '../utils/api';

  interface UserInfo {
    email: string;
    firstName: string;
    lastName: string;
    emailSubscription: boolean;
    createdAt: string | null;
    updatedAt: string | null;
  }

  let userInfo: UserInfo = {
    email: '',
    firstName: '',
    lastName: '',
    emailSubscription: true,
    createdAt: null,
    updatedAt: null
  };

  let loading = true;
  let saving = false;
  let message = '';
  let messageType: 'success' | 'error' | '' = '';

  // Form data
  let firstName = '';
  let lastName = '';
  let emailSubscription = true;

  onMount(async () => {
    await loadUserInfo();
  });

  async function loadUserInfo() {
    try {
      loading = true;
      const response = await fetchWithAuth('api/account/info');

      if (response.ok) {
        userInfo = await response.json();
        // Initialize form data
        firstName = userInfo.firstName || '';
        lastName = userInfo.lastName || '';
        emailSubscription = userInfo.emailSubscription;
      } else {
        showMessage('Failed to load account information', 'error');
      }
    } catch (error) {
      console.error('Error loading user info:', error);
      showMessage('Failed to load account information', 'error');
    } finally {
      loading = false;
    }
  }

  async function updatePersonalInfo() {
    try {
      saving = true;
      const response = await fetchWithAuth('api/account/info', {
        method: 'PUT',
        body: JSON.stringify({
          firstName: firstName.trim(),
          lastName: lastName.trim()
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        showMessage('Personal information updated successfully', 'success');
        await loadUserInfo(); // Refresh data
      } else {
        showMessage(result.message || 'Failed to update personal information', 'error');
      }
    } catch (error) {
      console.error('Error updating personal info:', error);
      showMessage('Failed to update personal information', 'error');
    } finally {
      saving = false;
    }
  }

  async function updateEmailSubscription() {
    try {
      saving = true;
      const response = await fetchWithAuth('api/account/email-subscription', {
        method: 'PUT',
        body: JSON.stringify({
          emailSubscription
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        showMessage(result.message, 'success');
        await loadUserInfo(); // Refresh data
      } else {
        showMessage(result.message || 'Failed to update email subscription', 'error');
      }
    } catch (error) {
      console.error('Error updating email subscription:', error);
      showMessage('Failed to update email subscription', 'error');
    } finally {
      saving = false;
    }
  }

  function showMessage(text: string, type: 'success' | 'error') {
    message = text;
    messageType = type;
    setTimeout(() => {
      message = '';
      messageType = '';
    }, 5000);
  }

  function formatDate(dateString: string | null): string {
    if (!dateString) return 'Not available';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
</script>

<div class="min-h-screen bg-dark text-white">
  <div class="max-w-4xl mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center gap-3 mb-2">
        <Settings class="h-8 w-8 text-primary" />
        <h1 class="text-3xl font-bold">Account Settings</h1>
      </div>
      <p class="text-gray-400">Manage your personal information and email preferences</p>
    </div>

    {#if loading}
      <div class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span class="ml-3 text-gray-400">Loading account information...</span>
      </div>
    {:else}
      <!-- Message Display -->
      {#if message}
        <div class="mb-6 p-4 rounded-lg flex items-center gap-3 {messageType === 'success' ? 'bg-green-900/20 border border-green-500/30 text-green-400' : 'bg-red-900/20 border border-red-500/30 text-red-400'}">
          {#if messageType === 'success'}
            <Check class="h-5 w-5" />
          {:else}
            <X class="h-5 w-5" />
          {/if}
          <span>{message}</span>
        </div>
      {/if}

      <div class="grid gap-8 lg:grid-cols-2">
        <!-- Personal Information -->
        <div class="bg-dark-lighter border border-gray-800/50 rounded-lg p-6">
          <div class="flex items-center gap-3 mb-6">
            <User class="h-6 w-6 text-primary" />
            <h2 class="text-xl font-semibold">Personal Information</h2>
          </div>

          <form on:submit|preventDefault={updatePersonalInfo} class="space-y-4">
            <div>
              <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={userInfo.email}
                disabled
                class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-gray-400 cursor-not-allowed"
              />
              <p class="text-xs text-gray-500 mt-1">Email address cannot be changed</p>
            </div>

            <div>
              <label for="firstName" class="block text-sm font-medium text-gray-300 mb-2">
                First Name
              </label>
              <input
                type="text"
                id="firstName"
                bind:value={firstName}
                maxlength="50"
                class="w-full px-3 py-2 bg-dark border border-gray-700 rounded-lg text-white focus:border-primary focus:ring-1 focus:ring-primary"
                placeholder="Enter your first name"
              />
            </div>

            <div>
              <label for="lastName" class="block text-sm font-medium text-gray-300 mb-2">
                Last Name
              </label>
              <input
                type="text"
                id="lastName"
                bind:value={lastName}
                maxlength="50"
                class="w-full px-3 py-2 bg-dark border border-gray-700 rounded-lg text-white focus:border-primary focus:ring-1 focus:ring-primary"
                placeholder="Enter your last name"
              />
            </div>

            <button
              type="submit"
              disabled={saving}
              class="w-full bg-primary hover:bg-primary-dark text-white font-medium py-2 px-4 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {#if saving}
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Saving...
              {:else}
                <Save class="h-4 w-4" />
                Save Personal Information
              {/if}
            </button>
          </form>
        </div>

        <!-- Email Preferences -->
        <div class="bg-dark-lighter border border-gray-800/50 rounded-lg p-6">
          <div class="flex items-center gap-3 mb-6">
            <Mail class="h-6 w-6 text-primary" />
            <h2 class="text-xl font-semibold">Email Preferences</h2>
          </div>

          <form on:submit|preventDefault={updateEmailSubscription} class="space-y-4">
            <div class="flex items-start gap-3">
              <input
                type="checkbox"
                id="emailSubscription"
                bind:checked={emailSubscription}
                class="mt-1 h-4 w-4 text-primary bg-dark border-gray-700 rounded focus:ring-primary focus:ring-2"
              />
              <div>
                <label for="emailSubscription" class="block text-sm font-medium text-gray-300">
                  Email Notifications
                </label>
                <p class="text-xs text-gray-500 mt-1">
                  Receive email notifications for price drops and product availability
                </p>
              </div>
            </div>

            <button
              type="submit"
              disabled={saving}
              class="w-full bg-primary hover:bg-primary-dark text-white font-medium py-2 px-4 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {#if saving}
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Saving...
              {:else}
                <Save class="h-4 w-4" />
                Save Email Preferences
              {/if}
            </button>
          </form>
        </div>
      </div>

      <!-- Account Information -->
      <div class="mt-8 bg-dark-lighter border border-gray-800/50 rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4">Account Information</h3>
        <div class="grid gap-4 sm:grid-cols-2">
          <div>
            <span class="text-sm text-gray-400">Member Since</span>
            <p class="text-white">{formatDate(userInfo.createdAt)}</p>
          </div>
          <div>
            <span class="text-sm text-gray-400">Last Updated</span>
            <p class="text-white">{formatDate(userInfo.updatedAt)}</p>
          </div>
        </div>
      </div>
    {/if}
  </div>
</div>
