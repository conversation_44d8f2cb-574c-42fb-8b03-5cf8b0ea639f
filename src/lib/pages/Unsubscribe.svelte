<script lang="ts">
  import { onMount } from 'svelte';
  import { CheckCircle, XCircle, Mail, AlertCircle, Loader } from 'lucide-svelte';
  import Button from '../components/ui/Button.svelte';
  
  let token = '';
  let email = '';
  let isValidToken = false;
  let isSubscribed = true;
  let loading = true;
  let processing = false;
  let error = '';
  let success = false;
  let unsubscribeReason = 'User requested';
  
  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:9090';
  
  onMount(() => {
    // Get token from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    token = urlParams.get('token') || '';
    
    if (token) {
      validateToken();
    } else {
      loading = false;
      error = 'No unsubscribe token provided';
    }
  });
  
  async function validateToken() {
    try {
      loading = true;
      error = '';
      
      const response = await fetch(`${API_URL}/api/public/unsubscribe/validate?token=${encodeURIComponent(token)}`);
      const data = await response.json();
      
      if (data.valid) {
        isValidToken = true;
        email = data.email;
        isSubscribed = data.isSubscribed;
      } else {
        error = data.message || 'Invalid unsubscribe link';
      }
    } catch (e) {
      error = 'Failed to validate unsubscribe link';
      console.error('Validation error:', e);
    } finally {
      loading = false;
    }
  }
  
  async function confirmUnsubscribe() {
    try {
      processing = true;
      error = '';
      
      const response = await fetch(`${API_URL}/api/public/unsubscribe/confirm`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          token: token,
          reason: unsubscribeReason
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        success = true;
        isSubscribed = false;
      } else {
        error = data.message || 'Failed to unsubscribe';
      }
    } catch (e) {
      error = 'An error occurred while processing your request';
      console.error('Unsubscribe error:', e);
    } finally {
      processing = false;
    }
  }
</script>

<svelte:head>
  <title>Unsubscribe | BargainHawk</title>
  <meta name="description" content="Unsubscribe from BargainHawk email notifications" />
  <meta name="robots" content="noindex, nofollow" />
</svelte:head>

<div class="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div class="text-center">
      <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
        <Mail class="h-6 w-6 text-blue-600" />
      </div>
      <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
        Email Unsubscribe
      </h2>
      <p class="mt-2 text-sm text-gray-600">
        Manage your email notification preferences
      </p>
    </div>

    <div class="bg-white shadow-lg rounded-lg p-8">
      {#if loading}
        <div class="text-center">
          <Loader class="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p class="text-gray-600">Validating unsubscribe request...</p>
        </div>
      
      {:else if error}
        <div class="text-center">
          <XCircle class="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 mb-2">Invalid Link</h3>
          <p class="text-gray-600 mb-6">{error}</p>
          <p class="text-sm text-gray-500">
            This link may have expired or is invalid. If you continue to receive unwanted emails, 
            please contact us at <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-500"><EMAIL></a>
          </p>
        </div>
      
      {:else if success}
        <div class="text-center">
          <CheckCircle class="h-12 w-12 text-green-500 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 mb-2">Successfully Unsubscribed</h3>
          <p class="text-gray-600 mb-4">
            <strong>{email}</strong> has been unsubscribed from all BargainHawk email notifications.
          </p>
          <p class="text-sm text-gray-500 mb-6">
            You will no longer receive price drop alerts, availability notifications, or promotional emails from us.
          </p>
          <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
            <p class="text-sm text-blue-800">
              <strong>Changed your mind?</strong> You can always re-subscribe by creating an account and managing your preferences in your dashboard.
            </p>
          </div>
        </div>
      
      {:else if isValidToken}
        <div>
          {#if !isSubscribed}
            <div class="text-center">
              <AlertCircle class="h-12 w-12 text-yellow-500 mx-auto mb-4" />
              <h3 class="text-lg font-medium text-gray-900 mb-2">Already Unsubscribed</h3>
              <p class="text-gray-600">
                <strong>{email}</strong> is already unsubscribed from BargainHawk email notifications.
              </p>
            </div>
          {:else}
            <div class="text-center mb-6">
              <h3 class="text-lg font-medium text-gray-900 mb-2">Confirm Unsubscribe</h3>
              <p class="text-gray-600">
                Are you sure you want to unsubscribe <strong>{email}</strong> from all BargainHawk email notifications?
              </p>
            </div>
            
            <div class="space-y-4">
              <div>
                <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">
                  Reason for unsubscribing (optional)
                </label>
                <select 
                  id="reason" 
                  bind:value={unsubscribeReason}
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="User requested">I no longer want these emails</option>
                  <option value="Too many emails">Too many emails</option>
                  <option value="Not relevant">Content not relevant to me</option>
                  <option value="Never signed up">I never signed up for this</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              
              <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <p class="text-sm text-yellow-800">
                  <strong>What you'll miss:</strong> Price drop alerts, back-in-stock notifications, and exclusive deals from our community.
                </p>
              </div>
              
              <div class="flex space-x-3">
                <Button 
                  variant="secondary" 
                  class="flex-1"
                  on:click={() => window.close()}
                >
                  Keep Subscribed
                </Button>
                <Button 
                  variant="primary" 
                  class="flex-1"
                  {processing}
                  on:click={confirmUnsubscribe}
                >
                  {processing ? 'Processing...' : 'Unsubscribe'}
                </Button>
              </div>
            </div>
          {/if}
        </div>
      {/if}
    </div>
    
    <div class="text-center">
      <p class="text-xs text-gray-500">
        <a href="https://bargainhawk.ca" class="text-blue-600 hover:text-blue-500">BargainHawk</a> - 
        Your Smart Shopping Companion
      </p>
    </div>
  </div>
</div>
