  <script lang="ts">
    import Hero from '../components/landing/Hero.svelte';
    import PriceDropsShowcase from '../components/landing/PriceDropsShowcase.svelte';
    import Features from '../components/landing/Features.svelte';
    import HowItWorks from '../components/landing/HowItWorks.svelte';
    import Testimonials from '../components/landing/Testimonials.svelte';
    import CTASection from '../components/landing/CTASection.svelte';
    import SupportedStores from '../components/SupportedStores.svelte';
    import ContextualLinks from '../components/seo/ContextualLinks.svelte';
    import FeaturedArticles from '../components/seo/FeaturedArticles.svelte';
    import AuthModal from '../components/AuthModal.svelte';

    let showAuthModal = false;
  </script>

  <svelte:head>
    <title>BargainHawk - Never Miss a Price Drop | Costco Tracker</title>
    <meta name="description" content="Track Costco prices automatically in Canada and USA. Get notified when prices drop and claim your price adjustment. Never miss a Costco price drop again!" />
    <meta name="keywords" content="costco price tracking, costco price adjustment, costco price drop alerts, costco.ca price tracker, costco canada price monitoring, walmart price tracking, walmart price adjustment, walmart price drop alerts" />
    <link rel="canonical" href="https://bargainhawk.ca/" />

    <!-- Open Graph Tags -->
    <meta property="og:title" content="BargainHawk - Never Miss a Price Drop | Costco Tracker" />
    <meta property="og:description" content="Track Costco prices automatically in Canada and USA. Get notified when prices drop and claim your price adjustment." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://bargainhawk.ca/" />
    <meta property="og:site_name" content="BargainHawk" />
    <meta property="og:image" content="https://bargainhawk.ca/logo.png" />
  </svelte:head>

  <main>
    <Hero on:showAuth={() => showAuthModal = true} />
    <PriceDropsShowcase />
    <Features />
    <HowItWorks />
    <Testimonials />
    <SupportedStores />

    <!-- Featured articles section for orphan blog posts -->
    <div class="px-4 sm:px-6 lg:px-8 py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto">
        <FeaturedArticles />
      </div>
    </div>

    <CTASection on:signup={() => showAuthModal = true} />

    <!-- Contextual links for SEO and user engagement -->
    <div class="px-4 sm:px-6 lg:px-8 pb-16">
      <div class="max-w-7xl mx-auto">
        <ContextualLinks pageType="general" />
      </div>
    </div>
  </main>

  <AuthModal
    show={showAuthModal}
    on:close={() => showAuthModal = false}
  />