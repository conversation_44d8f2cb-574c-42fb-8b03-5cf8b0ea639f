import { fetchWithAuth } from '../utils/api';
import type { UploadMetadata, ReceiptData } from '../types/receipt';
import { user } from '../stores/auth';
import { get } from 'svelte/store';

export async function getUserReceipts(): Promise<UploadMetadata[]> {
  const currentUser = get(user);
  if (!currentUser) {
    throw new Error('User not authenticated');
  }

  try {
    const response = await fetchWithAuth(`uploads/user/${currentUser.id}`);

    if (!response.ok) {
      if (response.status === 204 || response.status === 404) {
        // No receipts found - this is normal, not an error
        return [];
      }
      throw new Error('Failed to fetch receipts');
    }

    const data = await response.json();
    return Array.isArray(data) ? data : [];
  } catch (error) {
    console.error('Error fetching receipts:', error);

    // If it's a network error or server error, throw it
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error('Unable to connect to server');
    }

    // For other errors, return empty array instead of throwing
    // This prevents showing error messages when there are simply no receipts
    console.warn('Returning empty receipts array due to error:', error);
    return [];
  }
}

export async function getReceiptData(receiptId: string): Promise<ReceiptData | null> {
  try {
    const response = await fetchWithAuth(`receipts/${receiptId}`);
    
    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error('Failed to fetch receipt data');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching receipt data:', error);
    throw new Error('Failed to load receipt data');
  }
}