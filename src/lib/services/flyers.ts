import { fetchWithAuth } from '../utils/api';
import type { Flyer } from '../types/flyer';

export async function getLatestFlyer(vendorName: string = 'COSTCOCANADA'): Promise<Flyer> {
  try {
    const response = await fetchWithAuth(`api/public/flyers/latest?vendorName=${vendorName}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch flyer');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching flyer:', error);
    throw new Error('Failed to load flyer');
  }
}