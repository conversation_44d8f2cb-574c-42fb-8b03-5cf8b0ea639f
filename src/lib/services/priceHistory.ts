import { fetchWithAuth } from '../utils/api';
import { user } from '../stores/auth';
import { get } from 'svelte/store';
import type { PriceHistoryItem } from '../types/priceHistory';

export async function getPriceHistory(url: string): Promise<PriceHistoryItem[]> {
  try {
    const response = await fetchWithAuth(`api/price/history?url=${encodeURIComponent(url)}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch price history');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching price history:', error);
    throw new Error('Failed to load price history');
  }
}

export async function addPriceTracking(url: string): Promise<void> {
  const currentUser = get(user);
  if (!currentUser?.email) {
    throw new Error('User not authenticated');
  }

  try {
    const response = await fetchWithAuth('api/item', {
      method: 'POST',
      body: JSON.stringify({
        url,
        email: currentUser.email
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      
      if (response.status === 409) {
        throw new Error('You are already tracking this item');
      } else if (response.status === 400) {
        throw new Error(errorText);
      } else {
        throw new Error('Failed to add item tracking');
      }
    }
  } catch (error) {
    console.error('Error adding item tracking:', error);
    throw error instanceof Error ? error : new Error('Failed to add item tracking');
  }
}