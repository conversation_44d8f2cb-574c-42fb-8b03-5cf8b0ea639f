import type { PriceChange } from '../types/priceChange';
import { fetchWithAuth } from '../utils/api';

export async function getPriceChanges(): Promise<PriceChange[]> {
  try {
    const response = await fetchWithAuth('api/public/price/change');
    
    if (!response.ok) {
      throw new Error('Failed to fetch price changes');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching price changes:', error);
    throw new Error('Failed to load price changes');
  }
}