// New blog system - individual markdown files with frontmatter
export interface BlogPost {
  id: string;
  slug: string;
  title: string;
  description: string;
  date: string;
  content: string;
  keywords: string[];
  author: string;
  readingTime: string;
  category?: string;
  tags?: string[];
  featured?: boolean;
  lastModified?: string;
  canonicalUrl?: string;
  relatedPosts?: string[];
}

export interface BlogMetadata {
  id: string;
  slug: string;
  title: string;
  description: string;
  date: string;
  author: string;
  readingTime: string;
  category?: string;
  tags?: string[];
  featured?: boolean;
  lastModified?: string;
  keywords: string[];
  canonicalUrl?: string;
  relatedPosts?: string[];
}

// Blog post registry - metadata only
export const blogRegistry: BlogMetadata[] = [
  {
    id: '17',
    slug: 'costco-price-history-tracker',
    title: 'Costco Price History Tracker: Complete Guide to Tracking Costco Prices',
    description: 'Ultimate guide to Costco price history tracking. Learn how automated price monitoring saves you hundreds annually with alerts, price adjustments, and smart shopping strategies.',
    date: '2025-01-26',
    author: 'BargainHawk Team',
    readingTime: '13 min read',
    category: 'tools',
    tags: ['costco', 'price-tracking', 'price-history', 'automation', 'savings'],
    featured: true,
    keywords: [
      'costco price history tracker',
      'costco price tracking',
      'track costco prices',
      'costco price history',
      'costco price monitor',
      'costco price alerts',
      'costco price changes',
      'costco price tracker tool'
    ]
  },
  {
    id: '16',
    slug: 'costco-eye-exam-guide',
    title: 'How Much is a Costco Eye Exam? Complete Pricing Guide 2025',
    description: 'Complete guide to Costco eye exam pricing in Canada. Learn costs, what\'s included, booking tips, and how to save money on optical services and products.',
    date: '2025-01-26',
    author: 'BargainHawk Team',
    readingTime: '10 min read',
    category: 'guides',
    tags: ['costco', 'eye-exam', 'optical', 'pricing', 'healthcare'],
    featured: true,
    keywords: [
      'how much is costco eye exam',
      'how much is an eye exam at costco',
      'costco eye exam cost',
      'costco eye exam price',
      'costco optical pricing',
      'costco eye exam canada',
      'costco vision center cost',
      'costco eye test price'
    ]
  },
  {
    id: '15',
    slug: 'costco-membership-guide',
    title: 'Do You Need a Membership to Shop at Costco? Complete Guide 2025',
    description: 'Complete guide to Costco membership requirements, alternatives, and value analysis. Learn if a Costco membership is worth it and how to maximize your savings.',
    date: '2025-01-26',
    author: 'BargainHawk Team',
    readingTime: '11 min read',
    category: 'guides',
    tags: ['costco', 'membership', 'shopping-guide', 'savings', 'value-analysis'],
    featured: true,
    keywords: [
      'do you need a membership to shop at costco',
      'costco membership worth it',
      'costco membership cost',
      'costco membership benefits',
      'shop at costco without membership',
      'costco gold star vs executive',
      'costco membership guide',
      'costco membership requirements'
    ]
  },
  {
    id: '14',
    slug: 'best-deals-at-costco-guide',
    title: 'Best Deals at Costco This Week: Complete Guide + Price Tracking Strategy',
    description: 'Discover the best deals at Costco this week with our comprehensive guide. Learn deal hunting strategies, seasonal patterns, and how to track prices for maximum savings.',
    date: '2025-01-26',
    author: 'BargainHawk Team',
    readingTime: '12 min read',
    category: 'deals',
    tags: ['costco', 'deals', 'savings', 'weekly-deals', 'bargains'],
    featured: true,
    keywords: [
      'best deals at costco',
      'costco deals this week',
      'costco weekly deals',
      'costco sale items',
      'costco bargains',
      'costco clearance',
      'costco promotions',
      'costco instant savings'
    ]
  },
  {
    id: '13',
    slug: 'costco-coupon-book-guide',
    title: 'Costco Coupon Book 2025: Complete Guide + Price Tracking Strategy',
    description: 'Master the Costco coupon book with our complete 2025 guide. Learn release schedules, stacking strategies, and how to combine coupons with price tracking for maximum savings.',
    date: '2025-01-26',
    author: 'BargainHawk Team',
    readingTime: '11 min read',
    category: 'guides',
    tags: ['costco', 'coupons', 'savings', 'coupon-book', 'deals'],
    featured: true,
    keywords: [
      'costco coupon book',
      'costco coupons',
      'costco instant savings',
      'costco coupon book 2025',
      'costco coupon schedule',
      'costco member coupons',
      'costco executive coupons',
      'costco coupon deals'
    ]
  },
  {
    id: '12',
    slug: 'costco-hours-canada-guide',
    title: 'Costco Hours Canada: Complete Store Hours Guide + Smart Shopping Tips',
    description: 'Complete guide to Costco Canada store hours, holiday schedules, and best shopping times. Learn when to shop for maximum savings and how to track prices automatically.',
    date: '2025-01-26',
    author: 'BargainHawk Team',
    readingTime: '10 min read',
    category: 'guides',
    tags: ['costco', 'store-hours', 'shopping-tips', 'canada', 'savings'],
    featured: true,
    keywords: [
      'costco hours',
      'costco hours canada',
      'costco store hours',
      'costco holiday hours',
      'costco opening hours',
      'costco closing time',
      'best time to shop costco',
      'costco early hours'
    ]
  },
  {
    id: '11',
    slug: 'costco-return-policy-canada-guide',
    title: 'Costco Return Policy Canada: Complete 2025 Guide',
    description: 'Master Costco Canada\'s legendary return policy with our complete guide. Learn insider secrets, price adjustment strategies, and how to maximize your savings with smart shopping.',
    date: '2025-01-26',
    author: 'BargainHawk Team',
    readingTime: '12 min read',
    category: 'guides',
    tags: ['costco', 'returns', 'price-adjustments', 'canada', 'shopping-tips'],
    featured: true,
    keywords: [
      'costco return policy',
      'costco return policy canada',
      'costco returns',
      'costco price adjustment',
      'costco refund policy',
      'costco canada returns',
      'costco satisfaction guarantee',
      'costco return process'
    ]
  },
  {
    id: '10',
    slug: 'ai-powered-website-monitoring-2025',
    title: 'AI Website Monitoring: Smart Alternative to Traditional',
    description: 'Discover how AI-powered website change detection revolutionizes monitoring beyond basic alerts. Track price drops, content changes, and competitor moves with intelligent analysis that traditional tools can\'t match.',
    date: '2025-01-21',
    author: 'BargainHawk Team',
    readingTime: '12 min read',
    category: 'tools',
    tags: ['website-monitoring', 'ai', 'price-tracking', 'change-detection', 'automation'],
    featured: true,
    keywords: [
      'website monitoring',
      'change detection',
      'traditional monitoring alternative',
      'AI monitoring',
      'price tracking',
      'website alerts',
      'competitor monitoring',
      'automated monitoring',
      'smart website tracking',
      'AI website tracker',
      'intelligent monitoring',
      'website change alerts',
      'price drop detection',
      'competitor intelligence',
      'automated website monitoring'
    ],
    canonicalUrl: 'https://bargainhawk.ca/blog/ai-powered-website-monitoring-2025',
    relatedPosts: ['price-tracking-app-guide', 'best-deals-today']
  },
  {
    id: '1',
    slug: 'costco-price-adjustment-hack',
    title: 'Costco Canada Price Adjustments: Complete Guide 2025',
    description: 'Complete guide to Costco Canada\'s price adjustment policy. Learn how to automatically track Costco.ca prices and get refunds when prices drop within 30 days. Never miss savings again!',
    date: '2024-03-20',
    author: 'BargainHawk Team',
    readingTime: '5 min read',
    category: 'guides',
    tags: ['costco', 'price-adjustment', 'canada', 'savings', 'refund'],
    featured: true,
    keywords: [
      'costco canada price adjustment',
      'costco price adjustment policy',
      'costco.ca price match',
      'costco canada price tracking',
      'costco.ca price drop alerts',
      'costco canada price protection',
      'costco.ca refund policy',
      'costco canada automatic price tracking',
      'costco.ca price monitoring',
      'costco canada savings guide',
      'costco.ca deals tracker',
      'costco canada price tracker',
      'costco.ca price comparison',
      'costco canada money back guarantee',
      'costco.ca price adjustment policy 2025',
      'how to get costco price adjustment',
      'costco canada 30 day price protection',
      'costco.ca price drop refund',
      'costco canada receipt price match',
      'costco.ca automatic price tracking'
    ],
    canonicalUrl: 'https://bargainhawk.ca/blog/costco-price-adjustment-hack',
    relatedPosts: ['how-to-access-costco-receipts', 'costco-deals-canada']
  },
  {
    id: '2',
    slug: 'how-to-access-costco-receipts',
    title: 'How to Access Costco Receipts Online | BargainHawk',
    description: 'Learn how to access your Costco warehouse receipts online and discover how BargainHawk helps track price drops for your purchases. Save money easily!',
    date: '2025-02-11',
    author: 'BargainHawk Team',
    readingTime: '5 min read',
    category: 'guides',
    tags: ['costco', 'receipts', 'price-tracking'],
    keywords: [
      'costco receipts',
      'costco warehouse receipts',
      'track price drops',
      'costco price adjustment',
      'costco receipt online',
      'costco price monitoring',
      'price drop notifications',
      'bargainhawk',
      'costco canada receipts'
    ],
    canonicalUrl: 'https://bargainhawk.ca/blog/how-to-access-costco-receipts',
    relatedPosts: ['costco-price-adjustment-hack', 'costco-deals-canada']
  },
  {
    id: '3',
    slug: 'walmart-price-adjustment-guide',
    title: 'Walmart Price Adjustment Policy: Complete Guide',
    description: 'Complete guide to Walmart\'s price adjustment policy. Learn how to track Walmart prices automatically and never miss a price drop with BargainHawk\'s free price tracking tool.',
    date: '2025-01-15',
    author: 'BargainHawk Team',
    readingTime: '6 min read',
    category: 'guides',
    tags: ['walmart', 'price-adjustment', 'savings'],
    featured: true,
    keywords: [
      'walmart price adjustment',
      'walmart price match',
      'walmart price tracking',
      'walmart price drop alerts',
      'walmart price protection',
      'walmart refund policy',
      'walmart price monitoring',
      'walmart savings',
      'walmart deals',
      'walmart price tracker',
      'walmart automatic price tracking',
      'walmart price comparison',
      'walmart money back guarantee'
    ],
    canonicalUrl: 'https://bargainhawk.ca/blog/walmart-price-adjustment-guide',
    relatedPosts: ['walmart-deals-canada', 'price-comparison-canada']
  },
  {
    id: '13',
    slug: 'costco-deals-canada',
    title: 'Best Costco Deals Canada: Live Updates & Price Drop Alerts',
    description: 'Find the best Costco deals in Canada with live price tracking. Get instant alerts on warehouse savings, member exclusives, and price drops at Costco.ca.',
    date: '2025-01-16',
    author: 'BargainHawk Team',
    readingTime: '9 min read',
    category: 'deals',
    tags: ['costco', 'deals', 'canada', 'savings'],
    featured: true,
    keywords: [
      'costco deals canada',
      'costco canada deals',
      'costco.ca deals',
      'costco warehouse deals',
      'costco member deals',
      'costco price drops',
      'costco savings canada',
      'costco special offers',
      'costco clearance canada',
      'costco discount canada'
    ],
    canonicalUrl: 'https://bargainhawk.ca/blog/costco-deals-canada',
    relatedPosts: ['costco-price-adjustment-hack', 'how-to-access-costco-receipts']
  },
  {
    id: '14',
    slug: 'walmart-deals-canada',
    title: 'Best Walmart Deals Canada: Live Price Tracking',
    description: 'Find the best Walmart deals in Canada with live price tracking. Get instant alerts on rollbacks, clearance, and price drops at Walmart.ca.',
    date: '2025-01-16',
    author: 'BargainHawk Team',
    readingTime: '8 min read',
    category: 'deals',
    tags: ['walmart', 'deals', 'canada', 'rollbacks'],
    featured: true,
    keywords: [
      'walmart deals canada',
      'walmart canada deals',
      'walmart.ca deals',
      'walmart rollback deals',
      'walmart clearance canada',
      'walmart price drops',
      'walmart savings canada',
      'walmart special offers',
      'walmart discount canada',
      'walmart price tracker'
    ],
    canonicalUrl: 'https://bargainhawk.ca/blog/walmart-deals-canada',
    relatedPosts: ['walmart-price-adjustment-guide', 'price-comparison-canada']
  },
  {
    id: '15',
    slug: 'price-comparison-canada',
    title: 'Best Price Comparison Tools Canada: Complete Guide',
    description: 'Find the best price comparison tools for Canadian shoppers. Compare prices across Costco, Walmart, Amazon, and more. Never overpay again!',
    date: '2025-01-16',
    author: 'BargainHawk Team',
    readingTime: '11 min read',
    category: 'tools',
    tags: ['price-comparison', 'tools', 'canada', 'shopping'],
    featured: true,
    keywords: [
      'price comparison canada',
      'price comparison tools',
      'canadian price comparison',
      'compare prices canada',
      'price checker canada',
      'best price finder',
      'shopping comparison canada',
      'deal comparison tools',
      'price matching canada',
      'canadian shopping tools'
    ],
    canonicalUrl: 'https://bargainhawk.ca/blog/price-comparison-canada',
    relatedPosts: ['walmart-deals-canada', 'costco-deals-canada']
  }
];

// Dynamic content loading
export async function getBlogPost(slug: string): Promise<BlogPost | null> {
  try {
    // Import the markdown content dynamically
    const module = await import(`./posts/${slug}.md?raw`);
    const metadata = blogRegistry.find(post => post.slug === slug);
    
    if (!metadata) {
      return null;
    }

    return {
      ...metadata,
      content: module.default
    };
  } catch (error) {
    console.error(`Failed to load blog post: ${slug}`, error);
    return null;
  }
}

export function getAllBlogPosts(): BlogMetadata[] {
  return [...blogRegistry].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );
}

export function getBlogPostsByCategory(category: string): BlogMetadata[] {
  return blogRegistry
    .filter(post => post.category === category)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
}

export function getFeaturedBlogPosts(): BlogMetadata[] {
  return blogRegistry
    .filter(post => post.featured)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
}

export function getBlogPostsByTag(tag: string): BlogMetadata[] {
  return blogRegistry
    .filter(post => post.tags?.includes(tag))
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
}

export function getRelatedPosts(slug: string): BlogMetadata[] {
  const currentPost = blogRegistry.find(post => post.slug === slug);
  if (!currentPost?.relatedPosts) {
    return [];
  }
  
  return blogRegistry.filter(post => 
    currentPost.relatedPosts?.includes(post.slug)
  );
}

export const blogCategories = [
  { id: 'guides', name: 'Guides', description: 'Step-by-step tutorials and how-to guides' },
  { id: 'deals', name: 'Deals', description: 'Latest deals and savings opportunities' },
  { id: 'tools', name: 'Tools', description: 'Reviews and guides for money-saving tools' },
  { id: 'news', name: 'News', description: 'Industry news and updates' }
];
