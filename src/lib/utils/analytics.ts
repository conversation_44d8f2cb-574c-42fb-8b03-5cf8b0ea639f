import { page } from '../stores/navigation';

// Initialize dataLayer
declare global {
  interface Window {
    dataLayer: any[];
  }
}

window.dataLayer = window.dataLayer || [];
function gtag(...args: any[]) {
  window.dataLayer.push(arguments);
}

// Initialize Google Analytics
export function initializeAnalytics() {
  gtag('js', new Date());
  gtag('config', 'G-20E114N9VW');
}

// Track page views when navigation changes
page.subscribe((currentPage) => {
  if (typeof gtag === 'function') {
    gtag('event', 'page_view', {
      page_path: currentPage === 'home' ? '/' : `/${currentPage}`,
    });
  }
});