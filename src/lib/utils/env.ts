export function checkRequiredEnvVars() {
  const required = ['VITE_SUPABASE_URL', 'VITE_SUPABASE_ANON_KEY', 'VITE_API_URL'];
  const missing = required.filter(key => !import.meta.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

export const API_URL = import.meta.env.VITE_API_URL;

// Helper function to determine if we're in development mode
export const isDevelopment = import.meta.env.DEV;