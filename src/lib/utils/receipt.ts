import { supabase } from './supabase';
import { fetchWithAuth } from './api';

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_FILE_TYPES = ['image/jpeg', 'image/png'];
const ALLOWED_FILE_EXTENSIONS = ['jpg', 'jpeg', 'png'];

export function validateReceiptFile(file: File): string {
  if (file.size > MAX_FILE_SIZE) {
    return 'File size must be less than 5MB';
  }

  if (!ALLOWED_FILE_TYPES.includes(file.type)) {
    return 'Only JPEG and PNG files are allowed';
  }

  const extension = file.name.split('.').pop()?.toLowerCase();
  if (!extension || !ALLOWED_FILE_EXTENSIONS.includes(extension)) {
    return 'Only JPEG and PNG files are allowed';
  }

  return '';
}

export async function uploadReceipt(file: File): Promise<string> {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error('User not authenticated');

  const fileExt = file.name.split('.').pop();
  const fileName = `${user.id}/${Date.now()}.${fileExt}`;

  const { error: uploadError, data } = await supabase.storage
    .from('receipts')
    .upload(fileName, file, {
      cacheControl: '3600',
      upsert: false
    });

  if (uploadError) throw uploadError;
  if (!data?.path) throw new Error('Upload failed');

  // Get the public URL for the uploaded file
  const { data: { publicUrl } } = supabase.storage
    .from('receipts')
    .getPublicUrl(data.path);

  // Notify backend about the upload using fetchWithAuth
  const response = await fetchWithAuth('uploads/confirm-upload', {
    method: 'POST',
    body: JSON.stringify({
      userId: user.id,
      filePath: data.path,
      fileUrl: publicUrl,
      fileType: file.type,
      fileSize: file.size
    })
  });

  if (!response.ok) {
    // Delete the uploaded file if metadata storage fails
    // await supabase.storage.from('receipts').remove([data.path]);
    
    // For 400 Bad Request, show the specific error message
    if (response.status === 400) {
      const errorText = await response.text();
      throw new Error(errorText);
    }
    
    // For all other errors, show generic message
    throw new Error('Failed to confirm upload');
  }

  return data.path;
}