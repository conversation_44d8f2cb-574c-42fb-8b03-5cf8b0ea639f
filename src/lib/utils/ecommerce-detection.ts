/**
 * Utility functions for detecting supported e-commerce URLs
 * that would benefit from product tracking instead of website tracking
 */

export const SUPPORTED_ECOMMERCE_DOMAINS = [
  'costco.ca',
  'costco.com', 
  'wayfair.ca',
  'wayfair.com',
  'walmart.com'
] as const;

/**
 * Check if a URL belongs to a supported e-commerce site
 * that has dedicated product tracking capabilities
 */
export function isSupportedEcommerceUrl(url: string): boolean {
  if (!url) return false;

  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();

    console.log('isSupportedEcommerceUrl debug:', {
      url,
      hostname,
      supportedDomains: SUPPORTED_ECOMMERCE_DOMAINS
    });

    const isSupported = SUPPORTED_ECOMMERCE_DOMAINS.some(domain =>
      hostname === domain || hostname === `www.${domain}`
    );

    console.log('Domain match result:', isSupported);
    return isSupported;
  } catch (error) {
    console.error('Error parsing URL in isSupportedEcommerceUrl:', error, 'URL:', url);
    return false;
  }
}

/**
 * Get the store name from a supported e-commerce URL
 */
export function getStoreName(url: string): string {
  if (!url) return '';
  
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();
    
    if (hostname.includes('costco')) return 'Costco';
    if (hostname.includes('walmart')) return 'Walmart';
    if (hostname.includes('wayfair')) return 'Wayfair';
    
    return '';
  } catch {
    return '';
  }
}

/**
 * Check if URL appears to be a product page (has product indicators)
 */
export function looksLikeProductPage(url: string): boolean {
  if (!url) return false;

  const lowerUrl = url.toLowerCase();

  // Special handling for Wayfair - they often have complex URLs
  // For Wayfair, if it's not the homepage, it's likely a product or category page
  if (lowerUrl.includes('wayfair.')) {
    // Exclude obvious non-product pages
    const wayfairExclusions = [
      '/account',
      '/cart',
      '/checkout',
      '/help',
      '/about',
      '/contact',
      '/login',
      '/register',
      '/search',
      '/browse',
      '/sale',
      '/clearance'
    ];

    const isExcluded = wayfairExclusions.some(exclusion => lowerUrl.includes(exclusion));
    if (!isExcluded) {
      // If it has a path beyond the domain and isn't excluded, likely a product page
      try {
        const urlObj = new URL(url);
        const hasPath = urlObj.pathname.length > 1 && urlObj.pathname !== '/';
        console.log('Wayfair URL analysis:', { url, hasPath, pathname: urlObj.pathname });
        return hasPath;
      } catch {
        return false;
      }
    }
  }

  // Common product page patterns for other sites
  const productIndicators = [
    '/ip/', // Walmart item pages
    '/product/', // Generic product pages
    '/p/', // Short product URLs
    '/item/', // Item pages
    '/dp/', // Amazon-style (though we don't support Amazon)
    'product-', // Product in URL
    'item-', // Item in URL
    // Additional patterns
    '/pdp/', // Product detail pages
    '/pdx/', // Product pages
    '.product.', // Some sites use this pattern
  ];

  const hasProductIndicator = productIndicators.some(indicator => lowerUrl.includes(indicator));

  console.log('looksLikeProductPage debug:', {
    url,
    lowerUrl,
    productIndicators,
    hasProductIndicator
  });

  return hasProductIndicator;
}

/**
 * Determine if we should suggest product tracking for this URL
 */
export function shouldSuggestProductTracking(url: string): boolean {
  const isSupported = isSupportedEcommerceUrl(url);
  const looksLikeProduct = looksLikeProductPage(url);

  console.log('shouldSuggestProductTracking debug:', {
    url,
    isSupported,
    looksLikeProduct,
    result: isSupported && looksLikeProduct
  });

  return isSupported && looksLikeProduct;
}
