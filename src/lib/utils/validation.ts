export function getAllowedDomains(): string[] {
  return [
    'costco.ca',
    'costco.com',
    'wayfair.ca',
    'wayfair.com',
    'walmart.com'
  ];
}

export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export function isAllowedDomain(url: string): boolean {
  try {
    const urlObject = new URL(url);
    const domain = urlObject.hostname.toLowerCase();
    return getAllowedDomains().some(allowedDomain => 
      domain === allowedDomain || domain.endsWith('.' + allowedDomain)
    );
  } catch {
    return false;
  }
}

export function validateProductUrl(url: string): string {
  if (!url) {
    return 'Please enter a product URL';
  }
  
  if (!isValidUrl(url)) {
    return 'Please enter a valid URL';
  }
  
  if (!isAllowedDomain(url)) {
    const domains = getAllowedDomains();
    const formattedDomains = domains.slice(0, -1).join(', ') + ' and ' + domains.slice(-1);
    return `Currently, we only support ${formattedDomains} URLs`;
  }
  
  return '';
}

export function getValidationMessage(url: string, isAuthenticated: boolean): string {
  const urlError = validateProductUrl(url);
  if (urlError) return urlError;
  
  if (!isAuthenticated) {
    return 'Please create an account or sign in to continue';
  }
  
  return '';
}