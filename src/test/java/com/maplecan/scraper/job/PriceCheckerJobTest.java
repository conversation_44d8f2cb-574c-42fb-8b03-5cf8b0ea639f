package com.maplecan.scraper.job;

import com.maplecan.scraper.model.Item;
import com.maplecan.scraper.model.ScrapedData;
import com.maplecan.scraper.repository.ItemRepository;
import com.maplecan.scraper.repository.ScrapedDataRepository;
import com.maplecan.scraper.service.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PriceCheckerJobTest {

    @Mock
    private ScrapedDataRepository scrapedDataRepository;
    
    @Mock
    private ItemRepository itemRepository;
    
    @Mock
    private ItemService itemService;
    
    @Mock
    private ScrapingBeeService scrapingBeeService;
    
    @Mock
    private EmailService emailService;
    
    @Mock
    private EmailTemplateFactory emailTemplateFactory;
    
    @Mock
    private UnsubscribeService unsubscribeService;

    @InjectMocks
    private PriceCheckerJob priceCheckerJob;

    private ScrapedData scrapedData;
    private Item item;

    @BeforeEach
    void setUp() {
        scrapedData = ScrapedData.builder()
                .id("test-id")
                .url("https://example.com/product")
                .email("<EMAIL>")
                .productName("Test Product")
                .price(100.0)
                .userPrice(90.0)
                .lastScanTime(LocalDateTime.now().minusDays(1)) // Fresh data
                .build();

        item = Item.builder()
                .url("https://example.com/product")
                .latestPrice(85.0) // Price drop to trigger email
                .lastScanTime(LocalDateTime.now().minusDays(1))
                .isAvailable(true)
                .build();
    }

    @Test
    void testPriceDropEmailNotSentWhenDataIsStale() throws Exception {
        // Set up stale data (older than 7 days)
        scrapedData.setLastScanTime(LocalDateTime.now().minusDays(8));

        when(itemService.getByUrl(scrapedData.getUrl())).thenReturn(Optional.of(item));
        // Don't stub unsubscribeService since it won't be called when data is stale

        // Use reflection to call the private method
        ReflectionTestUtils.invokeMethod(priceCheckerJob, "processScrapedData", scrapedData);

        // Verify that no email was sent
        verify(emailService, never()).sendEmail(any());
        verify(emailTemplateFactory, never()).createPriceDropEmail(any(), any());
    }

    @Test
    void testPriceDropEmailSentWhenDataIsFresh() throws Exception {
        // Set up fresh data (within 7 days)
        scrapedData.setLastScanTime(LocalDateTime.now().minusDays(1));
        
        when(itemService.getByUrl(scrapedData.getUrl())).thenReturn(Optional.of(item));
        when(unsubscribeService.isUserSubscribed(scrapedData.getEmail())).thenReturn(true);
        when(emailTemplateFactory.createPriceDropEmail(any(), any())).thenReturn(
            com.maplecan.scraper.model.email.EmailRequest.builder()
                .subject("Test Subject")
                .build()
        );

        // Use reflection to call the private method
        ReflectionTestUtils.invokeMethod(priceCheckerJob, "processScrapedData", scrapedData);

        // Verify that email was sent
        verify(emailService, times(1)).sendEmail(any());
        verify(emailTemplateFactory, times(1)).createPriceDropEmail(any(), any());
    }

    @Test
    void testAvailabilityEmailNotSentWhenDataIsStale() throws Exception {
        // Set up stale data and availability change
        scrapedData.setLastScanTime(LocalDateTime.now().minusDays(8));
        scrapedData.setIsAvailable(false); // Was not available
        item.setIsAvailable(true); // Now available

        when(itemService.getByUrl(scrapedData.getUrl())).thenReturn(Optional.of(item));
        // Don't stub unsubscribeService since it won't be called when data is stale

        // Use reflection to call the private method
        ReflectionTestUtils.invokeMethod(priceCheckerJob, "processScrapedData", scrapedData);

        // Verify that no availability email was sent
        verify(emailService, never()).sendEmail(any());
        verify(emailTemplateFactory, never()).createAvailabilityEmail(any());
    }

    @Test
    void testDataFreshnessCheckWithNullLastScanTime() throws Exception {
        // Set lastScanTime to null (should allow email)
        scrapedData.setLastScanTime(null);
        
        when(itemService.getByUrl(scrapedData.getUrl())).thenReturn(Optional.of(item));
        when(unsubscribeService.isUserSubscribed(scrapedData.getEmail())).thenReturn(true);
        when(emailTemplateFactory.createPriceDropEmail(any(), any())).thenReturn(
            com.maplecan.scraper.model.email.EmailRequest.builder()
                .subject("Test Subject")
                .build()
        );

        // Use reflection to call the private method
        ReflectionTestUtils.invokeMethod(priceCheckerJob, "processScrapedData", scrapedData);

        // Verify that email was sent (null lastScanTime should not block email)
        verify(emailService, times(1)).sendEmail(any());
        verify(emailTemplateFactory, times(1)).createPriceDropEmail(any(), any());
    }
}
