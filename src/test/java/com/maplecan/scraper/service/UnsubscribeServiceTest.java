package com.maplecan.scraper.service;

import com.maplecan.scraper.model.UserMetadata;
import com.maplecan.scraper.repository.UserMetadataRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Optional;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UnsubscribeServiceTest {

    @Mock
    private UserMetadataRepository userMetadataRepository;

    @InjectMocks
    private UnsubscribeService unsubscribeService;

    private final String testEmail = "<EMAIL>";
    private final String testSecret = "test-secret-key-for-testing";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(unsubscribeService, "unsubscribeSecret", testSecret);
    }

    @Test
    void testGenerateUnsubscribeToken() {
        // When
        String token = unsubscribeService.generateUnsubscribeToken(testEmail);

        // Then
        assertNotNull(token);
        assertTrue(token.contains("."));
        String[] parts = token.split("\\.");
        assertEquals(2, parts.length);
    }

    @Test
    void testValidateAndExtractEmail_ValidToken() {
        // Given
        String token = unsubscribeService.generateUnsubscribeToken(testEmail);

        // When
        Optional<String> result = unsubscribeService.validateAndExtractEmail(token);

        // Then
        assertTrue(result.isPresent());
        assertEquals(testEmail, result.get());
    }

    @Test
    void testValidateAndExtractEmail_InvalidToken() {
        // Given
        String invalidToken = "invalid.token";

        // When
        Optional<String> result = unsubscribeService.validateAndExtractEmail(invalidToken);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void testValidateAndExtractEmail_ExpiredToken() {
        // Given - create a token with a timestamp from the past (more than 24 hours ago)
        long expiredTimestamp = System.currentTimeMillis() - (25 * 60 * 60 * 1000); // 25 hours ago
        String expiredPayload = testEmail + ":" + expiredTimestamp;
        String encodedPayload = Base64.getEncoder().encodeToString(expiredPayload.getBytes());

        // Create HMAC signature for the expired payload
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(testSecret.getBytes(), "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] signature = mac.doFinal(expiredPayload.getBytes());
            String encodedSignature = Base64.getEncoder().encodeToString(signature);
            String expiredToken = encodedPayload + "." + encodedSignature;

            // When
            Optional<String> result = unsubscribeService.validateAndExtractEmail(expiredToken);

            // Then
            assertTrue(result.isEmpty());
        } catch (Exception e) {
            fail("Failed to create expired token: " + e.getMessage());
        }
    }

    @Test
    void testUnsubscribeUser_NewUser() {
        // Given
        when(userMetadataRepository.findByEmail(testEmail)).thenReturn(Optional.empty());
        when(userMetadataRepository.save(any(UserMetadata.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // When
        boolean result = unsubscribeService.unsubscribeUser(testEmail, "Test reason");

        // Then
        assertTrue(result);
        verify(userMetadataRepository).save(argThat(metadata -> 
            metadata.getEmail().equals(testEmail) && 
            !metadata.getEmailSubscription() &&
            metadata.getUnsubscribeReason().equals("Test reason")
        ));
    }

    @Test
    void testUnsubscribeUser_ExistingUser() {
        // Given
        UserMetadata existingUser = UserMetadata.builder()
            .email(testEmail)
            .emailSubscription(true)
            .createdAt(LocalDateTime.now())
            .build();
        
        when(userMetadataRepository.findByEmail(testEmail)).thenReturn(Optional.of(existingUser));
        when(userMetadataRepository.save(any(UserMetadata.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // When
        boolean result = unsubscribeService.unsubscribeUser(testEmail, "Test reason");

        // Then
        assertTrue(result);
        verify(userMetadataRepository).save(argThat(metadata -> 
            metadata.getEmail().equals(testEmail) && 
            !metadata.getEmailSubscription() &&
            metadata.getUnsubscribeReason().equals("Test reason")
        ));
    }

    @Test
    void testIsUserSubscribed_SubscribedUser() {
        // Given
        UserMetadata subscribedUser = UserMetadata.builder()
            .email(testEmail)
            .emailSubscription(true)
            .build();
        
        when(userMetadataRepository.findByEmail(testEmail)).thenReturn(Optional.of(subscribedUser));

        // When
        boolean result = unsubscribeService.isUserSubscribed(testEmail);

        // Then
        assertTrue(result);
    }

    @Test
    void testIsUserSubscribed_UnsubscribedUser() {
        // Given
        UserMetadata unsubscribedUser = UserMetadata.builder()
            .email(testEmail)
            .emailSubscription(false)
            .build();
        
        when(userMetadataRepository.findByEmail(testEmail)).thenReturn(Optional.of(unsubscribedUser));

        // When
        boolean result = unsubscribeService.isUserSubscribed(testEmail);

        // Then
        assertFalse(result);
    }

    @Test
    void testIsUserSubscribed_NonExistentUser() {
        // Given
        when(userMetadataRepository.findByEmail(testEmail)).thenReturn(Optional.empty());

        // When
        boolean result = unsubscribeService.isUserSubscribed(testEmail);

        // Then
        assertTrue(result); // Default to subscribed for non-existent users
    }

    @Test
    void testUnsubscribeUser_DatabaseError() {
        // Given
        when(userMetadataRepository.findByEmail(testEmail)).thenThrow(new RuntimeException("Database error"));

        // When
        boolean result = unsubscribeService.unsubscribeUser(testEmail, "Test reason");

        // Then
        assertFalse(result);
    }
}
