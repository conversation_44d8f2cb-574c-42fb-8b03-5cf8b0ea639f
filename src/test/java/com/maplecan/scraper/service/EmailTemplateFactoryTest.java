package com.maplecan.scraper.service;

import com.maplecan.scraper.model.ScrapedData;
import com.maplecan.scraper.model.email.EmailRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class EmailTemplateFactoryTest {

//    @Test
//    public void testEmailTemplateCreation() {
//        EmailTemplateFactory factory = new EmailTemplateFactory();
//
//        // Create test data
//        ScrapedData testData = ScrapedData.builder()
//                .email("<EMAIL>")
//                .productName("Test Product")
//                .url("https://example.com/product")
//                .imageUrl("https://example.com/image.jpg")
//                .price(50.0)
//                .userPrice(60.0)
//                .createdTimestamp(LocalDateTime.now())
//                .build();
//
//        // Test that email creation works
//        EmailRequest emailRequest = factory.createPriceDropEmail(testData, 45.0);
//
//        assertNotNull(emailRequest, "Email request should not be null");
//        assertNotNull(emailRequest.getSubject(), "Subject should not be null");
//        assertNotNull(emailRequest.getHtmlContent(), "HTML content should not be null");
//        assertNotNull(emailRequest.getTextContent(), "Text content should not be null");
//        assertTrue(emailRequest.getToEmail().equals("<EMAIL>"), "To email should match");
//
//        System.out.printf("Email created successfully with subject: %s%n", emailRequest.getSubject());
//    }
    
//    @Test
//    public void testEnhancedEmailContainsProductImage() {
//        EmailTemplateFactory factory = new EmailTemplateFactory();
//
//        ScrapedData testData = ScrapedData.builder()
//                .email("<EMAIL>")
//                .productName("Test Product with Image")
//                .url("https://example.com/product")
//                .imageUrl("https://example.com/test-image.jpg")
//                .price(50.0)
//                .userPrice(60.0)
//                .build();
//
//        // Test multiple times to eventually get enhanced template
//        boolean foundEnhancedWithImage = false;
//        for (int i = 0; i < 20; i++) {
//            EmailRequest emailRequest = factory.createPriceDropEmail(testData, 45.0);
//
//            if (emailRequest.getSubject().contains("🎉")) {
//                assertTrue(emailRequest.getHtmlContent().contains("https://example.com/test-image.jpg"),
//                        "Enhanced email should contain product image URL");
//                assertTrue(emailRequest.getHtmlContent().contains("Test Product with Image"),
//                        "Enhanced email should contain product name");
//                foundEnhancedWithImage = true;
//                break;
//            }
//        }
//
//        assertTrue(foundEnhancedWithImage, "Should have found at least one enhanced email in 20 attempts");
//    }
    
//    @Test
//    public void testAvailabilityEmailABTesting() {
//        EmailTemplateFactory factory = new EmailTemplateFactory();
//
//        ScrapedData testData = ScrapedData.builder()
//                .email("<EMAIL>")
//                .productName("Back in Stock Product")
//                .url("https://example.com/product")
//                .imageUrl("https://example.com/image.jpg")
//                .price(75.0)
//                .build();
//
//        int enhancedCount = 0;
//        int legacyCount = 0;
//        int totalTests = 50;
//
//        for (int i = 0; i < totalTests; i++) {
//            EmailRequest emailRequest = factory.createAvailabilityEmail(testData);
//
//            if (emailRequest.getSubject().contains("🎯")) {
//                enhancedCount++;
//            } else {
//                legacyCount++;
//            }
//        }
//
//        System.out.printf("Availability A/B Test Results: Enhanced: %d%%, Legacy: %d%%%n",
//                enhancedCount * 2, legacyCount * 2); // *2 because we only ran 50 tests
//
//        assertTrue(enhancedCount > 10, "Enhanced availability template should be used");
//        assertTrue(legacyCount > 10, "Legacy availability template should be used");
//    }
}
