package com.maplecan.scraper.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class OpenAIVisionServiceTest {

    private OpenAIVisionService openAIVisionService;

    @BeforeEach
    void setUp() {
        openAIVisionService = new OpenAIVisionService();
        ReflectionTestUtils.setField(openAIVisionService, "openAiApiKey", "test-api-key");
        ReflectionTestUtils.setField(openAIVisionService, "openAiApiUrl", "https://api.openai.com/v1/chat/completions");
        ReflectionTestUtils.setField(openAIVisionService, "configuredModel", "gpt-4o");
    }

    @Test
    void testValidPriceExtraction() throws Exception {
        // Test normal price range using parseAnalysisResult method directly
        String validResponse = "{\"first_price\":450,\"second_price\":450,\"changed\":false}";

        Method parseMethod = OpenAIVisionService.class.getDeclaredMethod("parseAnalysisResult", String.class);
        parseMethod.setAccessible(true);

        var result = (OpenAIVisionService.PriceDropAnalysis) parseMethod.invoke(openAIVisionService, validResponse);

        assertNotNull(result);
        assertEquals(0.8, result.getConfidence()); // High confidence for valid prices
        assertEquals("450", result.getOldPrice());
        assertEquals("450", result.getNewPrice());
        assertFalse(result.isHasPriceDrop());
    }

    @Test
    void testPriceSanityCheckTooHigh() throws Exception {
        // Test price above 10,000 threshold
        String invalidResponse = "{\"first_price\":15000,\"second_price\":15000,\"changed\":false}";

        Method parseMethod = OpenAIVisionService.class.getDeclaredMethod("parseAnalysisResult", String.class);
        parseMethod.setAccessible(true);

        var result = (OpenAIVisionService.PriceDropAnalysis) parseMethod.invoke(openAIVisionService, invalidResponse);

        assertNotNull(result);
        assertEquals(0.0, result.getConfidence()); // Should be 0 due to sanity check failure
    }

    @Test
    void testPriceSanityCheckTooLow() throws Exception {
        // Test price below 1 threshold
        String invalidResponse = "{\"first_price\":0.5,\"second_price\":0.5,\"changed\":false}";

        Method parseMethod = OpenAIVisionService.class.getDeclaredMethod("parseAnalysisResult", String.class);
        parseMethod.setAccessible(true);

        var result = (OpenAIVisionService.PriceDropAnalysis) parseMethod.invoke(openAIVisionService, invalidResponse);

        assertNotNull(result);
        assertEquals(0.0, result.getConfidence()); // Should be 0 due to sanity check failure
    }

    @Test
    void testPriceDropDetection() throws Exception {
        // Test actual price drop scenario
        String priceDropResponse = "{\"first_price\":500,\"second_price\":400,\"changed\":true}";

        Method parseMethod = OpenAIVisionService.class.getDeclaredMethod("parseAnalysisResult", String.class);
        parseMethod.setAccessible(true);

        var result = (OpenAIVisionService.PriceDropAnalysis) parseMethod.invoke(openAIVisionService, priceDropResponse);

        assertNotNull(result);
        assertEquals(0.9, result.getConfidence()); // High confidence for price drop
        assertEquals("500", result.getOldPrice());
        assertEquals("400", result.getNewPrice());
        assertTrue(result.isHasPriceDrop());
    }

    @Test
    void testMultipleNumbersInImage() throws Exception {
        // Test that system picks main price when multiple numbers present
        // This simulates a product page with shipping costs, sale badges, etc.
        String mainPriceResponse = "{\"first_price\":299,\"second_price\":299,\"changed\":false}";

        Method parseMethod = OpenAIVisionService.class.getDeclaredMethod("parseAnalysisResult", String.class);
        parseMethod.setAccessible(true);

        var result = (OpenAIVisionService.PriceDropAnalysis) parseMethod.invoke(openAIVisionService, mainPriceResponse);

        assertNotNull(result);
        assertEquals(0.8, result.getConfidence());
        assertEquals("299", result.getOldPrice());
        assertEquals("299", result.getNewPrice());
        // Should extract main price (299) not shipping cost (15) or other numbers
    }

    @Test
    void testIsValidPriceMethod() throws Exception {
        // Test the isValidPrice helper method directly
        Method isValidPriceMethod = OpenAIVisionService.class.getDeclaredMethod("isValidPrice", String.class);
        isValidPriceMethod.setAccessible(true);

        // Valid prices
        assertTrue((Boolean) isValidPriceMethod.invoke(openAIVisionService, "450"));
        assertTrue((Boolean) isValidPriceMethod.invoke(openAIVisionService, "1"));
        assertTrue((Boolean) isValidPriceMethod.invoke(openAIVisionService, "10000"));
        assertTrue((Boolean) isValidPriceMethod.invoke(openAIVisionService, "99.99"));

        // Invalid prices
        assertFalse((Boolean) isValidPriceMethod.invoke(openAIVisionService, "0.5"));
        assertFalse((Boolean) isValidPriceMethod.invoke(openAIVisionService, "10001"));
        assertFalse((Boolean) isValidPriceMethod.invoke(openAIVisionService, "invalid"));
        assertFalse((Boolean) isValidPriceMethod.invoke(openAIVisionService, ""));
    }

    @Test
    void testNoPriceDropDetectionConsistency() throws Exception {
        // Test that when AI says no price drop, the hasPriceDrop field is false
        // This addresses the bug where emails showed "Price Drop Detected: Yes"
        // even when old and new prices were the same
        String noPriceDropResponse = "{\"first_price\":579.99,\"second_price\":579.99,\"changed\":false}";

        Method parseMethod = OpenAIVisionService.class.getDeclaredMethod("parseAnalysisResult", String.class);
        parseMethod.setAccessible(true);

        var result = (OpenAIVisionService.PriceDropAnalysis) parseMethod.invoke(openAIVisionService, noPriceDropResponse);

        assertNotNull(result);
        assertFalse(result.isHasPriceDrop()); // Should be false when changed=false
        assertEquals("579.99", result.getOldPrice());
        assertEquals("579.99", result.getNewPrice());
        assertEquals(0.8, result.getConfidence()); // High confidence for valid prices
    }
}
