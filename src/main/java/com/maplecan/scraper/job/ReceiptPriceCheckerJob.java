package com.maplecan.scraper.job;

import com.maplecan.scraper.model.Item;
import com.maplecan.scraper.model.ItemDetail;
import com.maplecan.scraper.model.ReceiptData;
import com.maplecan.scraper.repository.ItemRepository;
import com.maplecan.scraper.repository.ReceiptDataRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class ReceiptPriceCheckerJob {

    @Autowired
    private ReceiptDataRepository receiptRepository;

    @Autowired
    private ItemRepository itemRepository;

//    @Scheduled(cron = "0 */1 * * * *") // Run daily at 2 AM UTC
    @Scheduled(cron = "0 0 3 * * *") // Run daily at 2 AM UTC
    public void checkReceiptPrices() {
        log.info("Starting Receipt Price Checker Job...");

        // Get receipts from the last 3 months
        LocalDateTime threeMonthsAgo = LocalDateTime.now().minusMonths(3);
        List<ReceiptData> receipts = receiptRepository.findByProcessedTimeAfter(threeMonthsAgo);

        for (ReceiptData receipt : receipts) {
            log.info("Checking receipt ID: {}", receipt.getId());
            for (ItemDetail receiptItem : receipt.getItems().values()) {
                String itemNumber = "COSTCOCANADA" + receiptItem.getItemNumber();

                // Find the item in the item repository
                Optional<Item> optionalItem = itemRepository.findByItemNumber(itemNumber);

                if (optionalItem.isPresent()) {
                    Item item = optionalItem.get();

                    // Compare current price with receipt price
                    if (item.getLatestPrice() < receiptItem.getPriceAfterDiscount()) {
                        log.info("Price adjustment opportunity for item: {} in receipt: {}. Current price: {}, Receipt price: {}",
                                itemNumber, receipt.getId(), item.getLatestPrice(), receiptItem.getPriceAfterDiscount());

                        // Further processing logic (e.g., generate a report or send a notification)
                    }
                } else {
                    log.warn("Item not found in repository for itemNumber: {}", itemNumber);
                }
            }
        }

        log.info("Receipt Price Checker Job completed.");
    }
}
