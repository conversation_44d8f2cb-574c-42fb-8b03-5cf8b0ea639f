package com.maplecan.scraper.job;

import com.maplecan.scraper.model.Item;
import com.maplecan.scraper.model.ItemHistory;
import com.maplecan.scraper.model.PriceChange;
import com.maplecan.scraper.model.ScrapedData;
import com.maplecan.scraper.model.email.EmailRequest;
import com.maplecan.scraper.repository.ItemHistoryRepository;
import com.maplecan.scraper.repository.ItemRepository;
import com.maplecan.scraper.repository.PriceChangeRepository;
import com.maplecan.scraper.repository.ScrapedDataRepository;
import com.maplecan.scraper.service.EmailService;
import com.maplecan.scraper.service.ItemService;
import com.maplecan.scraper.service.ScrapingBeeService;
import net.bytebuddy.asm.Advice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class PriceHistoryJob {

    @Autowired
    private ItemHistoryRepository itemHistoryRepository; // Service to fetch the price

    @Autowired
    private ItemRepository itemRepository; // Service to fetch the price

    @Autowired
    private ItemService itemService;

    @Autowired
    private PriceChangeRepository priceChangeRepository;

    @Autowired
    private ScrapingBeeService scrapingBeeService;

    @Scheduled(cron = "0 30 22 * * *")
//    @Scheduled(cron = "0 * * * * *")
    public void checkPrices() {
        System.out.println("Starting price history...");

        List<Item> itemList = itemRepository.findAll();

        for (Item item : itemList) {
            if (!itemService.shouldScanItem(item, LocalDateTime.now())) {
                continue;
            }

            LocalDateTime dateTime = LocalDateTime.now().truncatedTo(ChronoUnit.HOURS);
            try {
                ScrapedData scrapedData = scrapingBeeService.fetch(item.getUrl(), ""); // Fetch current price
                if (scrapedData.getIsAvailable() != null && !scrapedData.getIsAvailable()) {
                    item.setIsAvailable(false);
                    item.setLastScanTime(dateTime);
                    item.setUpdatedTimestamp(dateTime);
                    if (item.getCreatedTimestamp() == null) {
                        item.setCreatedTimestamp(dateTime);
                    }
                    itemService.save(item);
                    continue;
                }
                ItemHistory history = ItemHistory.builder()
                        .price(scrapedData.getPrice())
                        .url(item.getUrl())
                        .imageUrl(scrapedData.getImageUrl())
                        .dateTime(dateTime)
                        .build();
                itemHistoryRepository.save(history);
                if (item.getLatestPrice() != null && !Objects.equals(scrapedData.getPrice(), item.getLatestPrice())) {
                    PriceChange priceChange = PriceChange.builder()
                            .previousPrice(item.getLatestPrice())
                            .newPrice(scrapedData.getPrice())
                            .createdDate(LocalDateTime.now())
                            .imageUrl(scrapedData.getImageUrl())
                            .url(item.getUrl())
                            .build();
                    priceChangeRepository.save(priceChange);
                    item.setLastPriceChangeTimestamp(dateTime);
                }
                if (null == item.getItemNumber()) {
                    item.setItemNumber(scrapedData.getItemNumber());
                }

                if (null == item.getIsAvailable()) {
                    item.setIsAvailable(scrapedData.getIsAvailable());
                }

                if (null == item.getImageUrl()) {
                    item.setImageUrl(scrapedData.getImageUrl());
                }

                if (null == item.getCreatedTimestamp()) {
                    item.setCreatedTimestamp(dateTime);
                }

                item.setLastScanTime(dateTime);
                item.setIsAvailable(scrapedData.getIsAvailable());
                item.setUpdatedTimestamp(dateTime);
                item.setLatestPrice(scrapedData.getPrice());
                itemRepository.save(item);
            } catch (Exception e) {
                System.err.printf("Failed to check price for %s: %s%n", item.getUrl(), e.getMessage());
            }
        }

        System.out.println("Price check completed.");
    }
}
