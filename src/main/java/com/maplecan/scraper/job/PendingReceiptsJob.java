package com.maplecan.scraper.job;

import com.maplecan.scraper.model.UploadMetadata;
import com.maplecan.scraper.service.ReceiptProcessingService;
import com.maplecan.scraper.repository.UploadMetadataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PendingReceiptsJob {

    @Autowired
    private UploadMetadataRepository uploadMetadataRepository;

    @Autowired
    private ReceiptProcessingService receiptProcessingService;

    @Scheduled(cron = "0 */15 * * * *") // Run every 15 minutes
    public void processPendingReceipts() {
        System.out.println("Processing pending receipts...");

        // Fetch all receipts with "pending" status
        List<UploadMetadata> pendingReceipts = uploadMetadataRepository.findByStatus("pending");

        for (UploadMetadata receipt : pendingReceipts) {
            try {
                // Process and save receipt data
                receiptProcessingService.processAndSaveReceipt(receipt.getId(), receipt.getFileUrl());

                // Update receipt status to "complete"
                receipt.setStatus("complete");
                uploadMetadataRepository.save(receipt);

                System.out.printf("Processed receipt: %s%n", receipt.getId());
            } catch (Exception e) {
                // Update receipt status to "complete"
                receipt.setStatus("failed");
                uploadMetadataRepository.save(receipt);
                System.err.printf("Failed to process receipt: %s, error: %s%n", receipt.getId(), e.getMessage());
            }
        }

        System.out.println("Receipt processing completed.");
    }
}
