package com.maplecan.scraper.job;

import com.maplecan.scraper.model.Flyers;
import com.maplecan.scraper.repository.FlyerRepository;
import com.maplecan.scraper.service.CostcoCanadaCouponScraper;
import com.maplecan.scraper.service.CostcoFlyerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDate;
import java.util.Optional;

@Service
public class FlyerUpdateJob {

    private static final String VENDOR_NAME = "COSTCOCANADA";
    private static final String POSTAL_CODE = "B4E3B6"; // Set Postal Code

    @Autowired
    private CostcoFlyerService costcoFlyerService;

    @Scheduled(cron = "0 0 12 * * *") // Run daily at 12:00 PM UTC
//    @Scheduled(cron = "0 * * * * *")
    public void updateFlyers() throws IOException {
        System.out.println("Starting flyer update check...");

        // Fetch the latest flyer for the vendor
        Optional<Flyers> latestFlyer = costcoFlyerService.getLatestFlyer(VENDOR_NAME);

        if (latestFlyer.isEmpty()) {
            // No flyer found, run the flyer scraper
            System.out.println("No existing flyer found. Fetching new flyer...");
            costcoFlyerService.scrapeAndSaveFlyer(POSTAL_CODE);
            return;
        }

        // Get the latest flyer
        Flyers flyer = latestFlyer.get();

        // Check if the flyer is expired
        LocalDate validToDate = LocalDate.parse(flyer.getValidTo()); // Convert to LocalDate
        LocalDate today = LocalDate.now();

        if (validToDate.isBefore(today)) {
            // The flyer is expired, run the scraper
            System.out.println("Existing flyer expired. Fetching new flyer...");
            costcoFlyerService.scrapeAndSaveFlyer(POSTAL_CODE);
//            costcoFlyerService.screenshotFlyer(POSTAL_CODE);
        } else {
            System.out.println("Flyer is still valid. No update needed.");
        }
    }
}
