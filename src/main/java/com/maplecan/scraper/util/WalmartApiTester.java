package com.maplecan.scraper.util;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * Simple tester for Walmart API calls
 */
public class WalmartApiTester {

    public static void main(String[] args) {
        WalmartApiTester tester = new WalmartApiTester();
        
        // Test with a known Walmart item ID
        String itemId = "4837473";
        String zipCode = "95054"; // San Francisco Bay Area
        
        System.out.println("=== TESTING WALMART API ===");
        System.out.println("Item ID: " + itemId);
        System.out.println("Zip Code: " + zipCode);
        System.out.println("========================");
        
        try {
            String result = tester.testWalmartApiCall(itemId, zipCode);
            System.out.println("SUCCESS! Walmart API Response:");
            System.out.println(result);
        } catch (Exception e) {
            System.err.println("ERROR: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public String testWalmartApiCall(String itemId, String zipCode) throws Exception {
        // Generate Walmart API headers
        WalmartApiSignatureGenerator generator = new WalmartApiSignatureGenerator();
        Map<String, String> walmartHeaders = generator.generateWalmartApiHeaders();
        
        // Create HTTP headers
        HttpHeaders headers = new HttpHeaders();
        headers.set("WM_CONSUMER.ID", walmartHeaders.get("WM_CONSUMER.ID"));
        headers.set("WM_CONSUMER.INTIMESTAMP", walmartHeaders.get("WM_CONSUMER.INTIMESTAMP"));
        headers.set("WM_SEC.KEY_VERSION", walmartHeaders.get("WM_SEC.KEY_VERSION"));
        headers.set("WM_SEC.AUTH_SIGNATURE", walmartHeaders.get("WM_SEC.AUTH_SIGNATURE"));
        headers.set("Accept", "application/json");
        headers.set("Content-Type", "application/json");
        
        System.out.println("Generated Headers:");
        walmartHeaders.forEach((key, value) -> 
            System.out.println(key + ": " + (key.contains("SIGNATURE") ? value.substring(0, 50) + "..." : value)));
        System.out.println();
        
        // Build URL - try without zipCode first to see if that's the issue
        String url = "https://developer.api.walmart.com/api-proxy/service/affil/product/v2/items/" + itemId +
                     "?format=json";
        
        System.out.println("API URL: " + url);
        System.out.println();
        
        // Make the API call
        RestTemplate restTemplate = new RestTemplate();
        HttpEntity<String> entity = new HttpEntity<>(headers);

        try {
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            System.out.println("Response Status: " + response.getStatusCode());
            System.out.println("Response Headers: " + response.getHeaders());
            System.out.println();

            return response.getBody();
        } catch (Exception e) {
            System.err.println("API call failed: " + e.getMessage());

            // Try to get more details about the error
            if (e instanceof org.springframework.web.client.HttpClientErrorException) {
                org.springframework.web.client.HttpClientErrorException httpError =
                    (org.springframework.web.client.HttpClientErrorException) e;
                System.err.println("Status Code: " + httpError.getStatusCode());
                System.err.println("Response Headers: " + httpError.getResponseHeaders());
                System.err.println("Raw Response Body: " + httpError.getResponseBodyAsString());
            }
            throw e;
        }
    }
}
