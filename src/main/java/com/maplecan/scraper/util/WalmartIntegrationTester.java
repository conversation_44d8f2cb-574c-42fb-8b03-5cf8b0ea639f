package com.maplecan.scraper.util;

import com.maplecan.scraper.model.ScrapedData;
import com.maplecan.scraper.service.WalmartScraper;
import com.maplecan.scraper.service.WalmartUrlParser;
import com.maplecan.scraper.service.WalmartApiService;
import org.springframework.web.client.RestTemplate;

/**
 * Test the complete Walmart integration: URL parsing + API lookup
 */
public class WalmartIntegrationTester {

    public static void main(String[] args) {
        WalmartIntegrationTester tester = new WalmartIntegrationTester();
        
        // Test URLs
        String[] testUrls = {
            "https://www.walmart.com/ip/Step2-Deluxe-Canyon-Road-Train-Table-and-Chair-Set-Neutral-Kids-Outdoor-Activity-Table/4837473",
            "https://walmart.com/ip/4837473",
            "https://www.walmart.com/ip/Product-Name/4837473",
            "https://www.walmart.com/ip/Free-Assembly-Women-s-and-Women-s-Plus-Smocked-Cotton-Mini-Dress-with-Flutter-Sleeves-Sizes-XS-4X/14855162805?classType=VARIANT&athbdg=L1600"
        };
        
        System.out.println("=== WALMART INTEGRATION TEST ===");
        System.out.println("Testing URL parsing + API integration");
        System.out.println("===================================");
        
        for (String url : testUrls) {
            System.out.println("\n--- Testing URL: " + url + " ---");
            tester.testWalmartIntegration(url);
        }
        
        // Test item number lookup
        System.out.println("\n--- Testing Item Number Lookup ---");
        tester.testItemNumberLookup("4837473");

        // Test longer item ID
        System.out.println("\n--- Testing Longer Item ID ---");
        tester.testItemNumberLookup("14855162805");
    }

    public void testWalmartIntegration(String url) {
        try {
            // Create services (normally injected by Spring)
            WalmartUrlParser urlParser = new WalmartUrlParser();
            WalmartApiService apiService = new WalmartApiService();
            WalmartScraper scraper = createWalmartScraper(urlParser, apiService);
            
            // Test URL parsing
            System.out.println("1. Testing URL parsing...");
            String itemId = urlParser.extractAndValidateItemId(url);
            System.out.println("   Extracted Item ID: " + itemId);
            
            if (itemId == null) {
                System.out.println("   ❌ Failed to extract item ID");
                return;
            }
            
            // Test full scraper integration
            System.out.println("2. Testing scraper integration...");
            ScrapedData result = scraper.fetch(url, "CA");
            
            if (result != null) {
                System.out.println("   ✅ Success! Product data:");
                System.out.println("   - Product Name: " + result.getProductName());
                System.out.println("   - Price: $" + result.getPrice());
                System.out.println("   - Item Number: " + result.getItemNumber());
                System.out.println("   - Available: " + result.getIsAvailable());
                System.out.println("   - Image URL: " + (result.getImageUrl() != null ? "Present" : "Missing"));
            } else {
                System.out.println("   ❌ Failed to fetch product data");
            }
            
        } catch (Exception e) {
            System.out.println("   ❌ Error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public void testItemNumberLookup(String itemNumber) {
        try {
            // Create services
            WalmartUrlParser urlParser = new WalmartUrlParser();
            WalmartApiService apiService = new WalmartApiService();
            WalmartScraper scraper = createWalmartScraper(urlParser, apiService);
            
            System.out.println("Testing item number: " + itemNumber);
            
            ScrapedData result = scraper.fetchByItemNumber(itemNumber);
            
            if (result != null) {
                System.out.println("✅ Success! Product data:");
                System.out.println("- Product Name: " + result.getProductName());
                System.out.println("- Price: $" + result.getPrice());
                System.out.println("- Item Number: " + result.getItemNumber());
                System.out.println("- Available: " + result.getIsAvailable());
                System.out.println("- URL: " + result.getUrl());
            } else {
                System.out.println("❌ Failed to fetch product data");
            }
            
        } catch (Exception e) {
            System.out.println("❌ Error: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Creates WalmartScraper with dependencies (simulating Spring injection)
     */
    private WalmartScraper createWalmartScraper(WalmartUrlParser urlParser, WalmartApiService apiService) {
        WalmartScraper scraper = new WalmartScraper();
        
        // Use reflection to set the dependencies (since we can't use @Autowired here)
        try {
            java.lang.reflect.Field apiField = WalmartScraper.class.getDeclaredField("walmartApiService");
            apiField.setAccessible(true);
            apiField.set(scraper, apiService);
            
            java.lang.reflect.Field parserField = WalmartScraper.class.getDeclaredField("walmartUrlParser");
            parserField.setAccessible(true);
            parserField.set(scraper, urlParser);
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject dependencies", e);
        }
        
        return scraper;
    }
}
