package com.maplecan.scraper.util;

import org.springframework.http.*;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.Map;
import java.util.zip.GZIPInputStream;

/**
 * Debug tool for Walmart API with proper gzip handling
 */
public class WalmartApiDebugger {

    public static void main(String[] args) {
        WalmartApiDebugger debugger = new WalmartApiDebugger();
        
        // Test with a known Walmart item ID
        String itemId = "4837473";
        
        System.out.println("=== WALMART API DEBUG TEST ===");
        System.out.println("Item ID: " + itemId);
        System.out.println("Consumer ID: d7cc8a42-4c30-4ca6-82c3-5cdab55053c8");
        System.out.println("===============================");
        
        try {
            String result = debugger.testWalmartApiWithGzipHandling(itemId);
            System.out.println("SUCCESS! Walmart API Response:");
            System.out.println(result);
        } catch (Exception e) {
            System.err.println("ERROR: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public String testWalmartApiWithGzipHandling(String itemId) throws Exception {
        // Generate Walmart API headers
        WalmartApiSignatureGenerator generator = new WalmartApiSignatureGenerator();
        Map<String, String> walmartHeaders = generator.generateWalmartApiHeaders();
        
        // Create HTTP headers
        HttpHeaders headers = new HttpHeaders();
        headers.set("WM_CONSUMER.ID", walmartHeaders.get("WM_CONSUMER.ID"));
        headers.set("WM_CONSUMER.INTIMESTAMP", walmartHeaders.get("WM_CONSUMER.INTIMESTAMP"));
        headers.set("WM_SEC.KEY_VERSION", walmartHeaders.get("WM_SEC.KEY_VERSION"));
        headers.set("WM_SEC.AUTH_SIGNATURE", walmartHeaders.get("WM_SEC.AUTH_SIGNATURE"));
        headers.set("Accept", "application/json");
        headers.set("Accept-Encoding", "gzip, deflate");
        headers.set("User-Agent", "BargainHawk-Scraper/1.0");
        
        System.out.println("Generated Headers:");
        walmartHeaders.forEach((key, value) -> 
            System.out.println(key + ": " + (key.contains("SIGNATURE") ? value.substring(0, 50) + "..." : value)));
        System.out.println();
        
        // Build URL - try the simplest possible request first
        String url = "https://developer.api.walmart.com/api-proxy/service/affil/product/v2/items/" + itemId;
        
        System.out.println("API URL: " + url);
        System.out.println();
        
        // Create RestTemplate with logging
        RestTemplate restTemplate = new RestTemplate();
        
        // Add request/response logging
        restTemplate.setInterceptors(Collections.singletonList(
            (ClientHttpRequestInterceptor) (request, body, execution) -> {
                System.out.println("=== REQUEST ===");
                System.out.println("URI: " + request.getURI());
                System.out.println("Method: " + request.getMethod());
                System.out.println("Headers: " + request.getHeaders());
                System.out.println("Body: " + new String(body));
                System.out.println();
                
                ClientHttpResponse response = execution.execute(request, body);
                
                System.out.println("=== RESPONSE ===");
                System.out.println("Status: " + response.getStatusCode());
                System.out.println("Headers: " + response.getHeaders());
                
                return response;
            }
        ));
        
        HttpEntity<String> entity = new HttpEntity<>(headers);
        
        try {
            ResponseEntity<byte[]> response = restTemplate.exchange(url, HttpMethod.GET, entity, byte[].class);
            
            System.out.println("Response Status: " + response.getStatusCode());
            System.out.println("Response Headers: " + response.getHeaders());
            
            // Handle gzipped response
            byte[] responseBody = response.getBody();
            if (responseBody != null) {
                String contentEncoding = response.getHeaders().getFirst("Content-Encoding");
                if ("gzip".equals(contentEncoding)) {
                    return decompressGzip(responseBody);
                } else {
                    return new String(responseBody);
                }
            }
            return "Empty response";
            
        } catch (Exception e) {
            System.err.println("API call failed: " + e.getMessage());
            
            // Try to get more details about the error
            if (e instanceof org.springframework.web.client.HttpClientErrorException) {
                org.springframework.web.client.HttpClientErrorException httpError = 
                    (org.springframework.web.client.HttpClientErrorException) e;
                System.err.println("Status Code: " + httpError.getStatusCode());
                System.err.println("Response Headers: " + httpError.getResponseHeaders());
                
                // Try to decompress the error response
                byte[] errorBody = httpError.getResponseBodyAsByteArray();
                if (errorBody != null && errorBody.length > 0) {
                    try {
                        String decompressed = decompressGzip(errorBody);
                        System.err.println("Decompressed Error Response: " + decompressed);
                    } catch (Exception gzipError) {
                        System.err.println("Raw Error Response: " + new String(errorBody));
                        System.err.println("Gzip decompression failed: " + gzipError.getMessage());
                    }
                }
            }
            throw e;
        }
    }

    private String decompressGzip(byte[] gzippedData) throws IOException {
        try (GZIPInputStream gzipInputStream = new GZIPInputStream(new ByteArrayInputStream(gzippedData));
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzipInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
            
            return outputStream.toString("UTF-8");
        }
    }
}
