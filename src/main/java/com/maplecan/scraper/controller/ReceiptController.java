package com.maplecan.scraper.controller;

import com.maplecan.scraper.model.ReceiptData;
import com.maplecan.scraper.model.UploadMetadata;
import com.maplecan.scraper.repository.ReceiptDataRepository;
import com.maplecan.scraper.repository.UploadMetadataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController
@RequestMapping("/receipts")
public class ReceiptController {

    @Autowired
    private UploadMetadataRepository uploadMetadataRepository;

    @Autowired
    private ReceiptDataRepository receiptDataRepository;

    /**
     * Fetch receipt data if the status is not "pending".
     *
     * @param receiptId the ID of the receipt
     * @return ResponseEntity with receipt data or a 404 error
     */
    @GetMapping("/{receiptId}")
    public ResponseEntity<ReceiptData> getReceiptData(@PathVariable String receiptId) {
        // Check if the receipt exists in the upload metadata
        Optional<UploadMetadata> uploadMetadata = uploadMetadataRepository.findById(receiptId);
        if (uploadMetadata.isEmpty() || "pending".equalsIgnoreCase(uploadMetadata.get().getStatus())) {
            return ResponseEntity.status(404).body(null); // Return 404 if pending or not found
        }

        // Fetch the receipt data
        Optional<ReceiptData> receiptData = receiptDataRepository.findByReceiptId(receiptId);
        return receiptData.map(ResponseEntity::ok)
                .orElseGet(() -> ResponseEntity.status(404).body(null)); // Return 404 if not found
    }
}
