package com.maplecan.scraper.controller;

import com.maplecan.scraper.dto.CommentResponse;
import com.maplecan.scraper.dto.PostResponse;
import com.maplecan.scraper.service.CommunityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * SEO-friendly public endpoints for community content
 * These endpoints are accessible without authentication for search engine crawling
 */
@Slf4j
@RestController
@RequestMapping("/public")
public class SeoController {

    @Autowired
    private CommunityService communityService;

    /**
     * Get public community posts for SEO
     * Accessible at /public/community/posts
     */
    @GetMapping("/community/posts")
    public ResponseEntity<Page<PostResponse>> getPublicCommunityPosts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String store,
            @RequestParam(required = false) String category,
            @RequestParam(defaultValue = "new") String sort) {
        try {
            log.info("Fetching public community posts - page: {}, size: {}, store: {}, category: {}, sort: {}", 
                    page, size, store, category, sort);
            Page<PostResponse> posts = communityService.getPosts(page, size, store, category, sort);
            return ResponseEntity.ok(posts);
        } catch (Exception e) {
            log.error("Error fetching public community posts", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get a specific public community post for SEO
     * Accessible at /public/community/posts/{postId}
     */
    @GetMapping("/community/posts/{postId}")
    public ResponseEntity<?> getPublicCommunityPost(@PathVariable String postId) {
        try {
            log.info("Fetching public community post: {}", postId);
            PostResponse post = communityService.getPost(postId);
            return ResponseEntity.ok(post);
        } catch (IllegalArgumentException e) {
            log.warn("Post not found: {}", postId);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error fetching public community post: {}", postId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get post"));
        }
    }

    /**
     * Get comments for a specific post for SEO
     * Accessible at /public/community/comments/{postId}
     */
    @GetMapping("/community/comments/{postId}")
    public ResponseEntity<?> getPublicCommunityComments(@PathVariable String postId) {
        try {
            log.info("Fetching public community comments for post: {}", postId);
            List<CommentResponse> comments = communityService.getComments(postId);
            return ResponseEntity.ok(comments);
        } catch (IllegalArgumentException e) {
            log.warn("Post not found for comments: {}", postId);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error fetching public community comments for post: {}", postId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get comments"));
        }
    }
}
