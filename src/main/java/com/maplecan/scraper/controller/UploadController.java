package com.maplecan.scraper.controller;

import com.maplecan.scraper.model.UploadMetadata;
import com.maplecan.scraper.service.UploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/uploads")
public class UploadController {

    @Autowired
    private UploadService uploadService;

    /**
     * Endpoint to confirm the file upload and save its metadata.
     *
     * @param payload A map containing the upload metadata: userId, filePath, fileUrl, fileType.
     * @return ResponseEntity with the status of the operation.
     */
    @PostMapping("/confirm-upload")
    public ResponseEntity<String> confirmUpload(@RequestBody Map<String, String> payload) {
        String userId = payload.get("userId");
        String filePath = payload.get("filePath");
        String fileUrl = payload.get("fileUrl");
        String fileType = payload.get("fileType");
        String email2 = (String) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        log.info("Getting product list for {}", email2);

        List<UploadMetadata> uploads = uploadService.getUploadsByUserId(userId);
        if (uploads.size() >= 2) {
            return ResponseEntity.badRequest().body("Every user is only allowed 2 or less receipts in this BETA mode.");
        }

        if (userId == null || filePath == null || fileUrl == null || fileType == null) {
            return ResponseEntity.badRequest().body("Missing required fields");
        }

        // Save metadata to MongoDB
        uploadService.saveUploadMetadata(email2, userId, filePath, fileUrl, fileType);

        return ResponseEntity.ok("Upload confirmed and metadata saved.");
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<List<UploadMetadata>> getUploadsByUserId(@PathVariable String userId) {
        List<UploadMetadata> uploads = uploadService.getUploadsByUserId(userId);
        if (uploads.isEmpty()) {
            return ResponseEntity.noContent().build(); // Return 204 No Content if no uploads are found
        }
        return ResponseEntity.ok(uploads);
    }
}
