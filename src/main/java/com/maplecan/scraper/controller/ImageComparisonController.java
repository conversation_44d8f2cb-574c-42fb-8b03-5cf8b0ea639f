package com.maplecan.scraper.controller;

import com.maplecan.scraper.dto.ImageComparisonResponse;
import com.maplecan.scraper.model.ImageComparisonResult;
import com.maplecan.scraper.service.ImageComparisonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

@Slf4j
@RestController
@RequestMapping("/api/public/image-comparison")
public class ImageComparisonController {

    @Autowired
    private ImageComparisonService imageComparisonService;

    /**
     * Compare two images and return similarity metrics
     */
    @GetMapping("/compare")
    public ResponseEntity<?> compareImages(
            @RequestParam String image1Url,
            @RequestParam String image2Url,
            @RequestParam(defaultValue = "false") boolean includeDifferenceMap) {
        
        try {
            log.info("Comparing images: {} vs {}", image1Url, image2Url);
            
            ImageComparisonResult result = imageComparisonService.compareImages(image1Url, image2Url);
            ImageComparisonResponse response = ImageComparisonResponse.fromResult(result);
            
            // Optionally include difference map URL
            if (includeDifferenceMap && result.getDifferenceMap() != null) {
                // In a real implementation, you might save the difference map to a file storage
                // and return a URL. For now, we'll indicate it's available via separate endpoint
                response.setDifferenceMapUrl("/api/public/image-comparison/difference-map?image1=" + 
                                           java.net.URLEncoder.encode(image1Url, "UTF-8") + 
                                           "&image2=" + java.net.URLEncoder.encode(image2Url, "UTF-8"));
            }
            
            log.info("Image comparison completed. Overall similarity: {}", 
                    result.getSimilarityPercentage());
            
            return ResponseEntity.ok(response);
            
        } catch (IOException e) {
            log.error("Error comparing images", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("Error processing images: " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error during image comparison", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Internal server error: " + e.getMessage());
        }
    }

    /**
     * Get the difference map as an image
     */
    @GetMapping("/difference-map")
    public ResponseEntity<byte[]> getDifferenceMap(
            @RequestParam String image1,
            @RequestParam String image2,
            @RequestParam(defaultValue = "png") String format) {
        
        try {
            log.info("Generating difference map for: {} vs {}", image1, image2);
            
            ImageComparisonResult result = imageComparisonService.compareImages(image1, image2);
            BufferedImage differenceMap = result.getDifferenceMap();
            
            if (differenceMap == null) {
                return ResponseEntity.notFound().build();
            }
            
            // Convert BufferedImage to byte array
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(differenceMap, format.toLowerCase(), baos);
            byte[] imageBytes = baos.toByteArray();
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("image/" + format.toLowerCase()));
            headers.setContentLength(imageBytes.length);
            headers.set("Content-Disposition", "inline; filename=difference-map." + format.toLowerCase());
            
            return new ResponseEntity<>(imageBytes, headers, HttpStatus.OK);
            
        } catch (IOException e) {
            log.error("Error generating difference map", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        } catch (Exception e) {
            log.error("Unexpected error generating difference map", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Quick similarity check - returns just a boolean and percentage
     */
    @GetMapping("/quick-check")
    public ResponseEntity<?> quickSimilarityCheck(
            @RequestParam String image1Url,
            @RequestParam String image2Url,
            @RequestParam(defaultValue = "0.95") double threshold) {
        
        try {
            log.info("Quick similarity check: {} vs {} (threshold: {})", 
                    image1Url, image2Url, threshold);
            
            ImageComparisonResult result = imageComparisonService.compareImages(image1Url, image2Url);
            
            QuickCheckResponse response = QuickCheckResponse.builder()
                    .areSimilar(result.getOverallSimilarity() >= threshold)
                    .similarityScore(result.getOverallSimilarity())
                    .similarityPercentage(result.getSimilarityPercentage())
                    .threshold(threshold)
                    .assessment(result.getAssessment())
                    .build();
            
            return ResponseEntity.ok(response);
            
        } catch (IOException e) {
            log.error("Error in quick similarity check", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("Error processing images: " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in quick similarity check", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Internal server error: " + e.getMessage());
        }
    }

    /**
     * Get supported image formats
     */
    @GetMapping("/supported-formats")
    public ResponseEntity<SupportedFormatsResponse> getSupportedFormats() {
        SupportedFormatsResponse response = SupportedFormatsResponse.builder()
                .inputFormats(new String[]{"jpg", "jpeg", "png", "gif", "bmp", "webp"})
                .outputFormats(new String[]{"png", "jpg", "jpeg"})
                .maxImageSize("10MB")
                .recommendedSize("Under 2MB for best performance")
                .build();
        
        return ResponseEntity.ok(response);
    }

    // Inner classes for response DTOs
    @lombok.Data
    @lombok.Builder
    public static class QuickCheckResponse {
        private boolean areSimilar;
        private double similarityScore;
        private String similarityPercentage;
        private double threshold;
        private String assessment;
    }

    @lombok.Data
    @lombok.Builder
    public static class SupportedFormatsResponse {
        private String[] inputFormats;
        private String[] outputFormats;
        private String maxImageSize;
        private String recommendedSize;
    }
}
