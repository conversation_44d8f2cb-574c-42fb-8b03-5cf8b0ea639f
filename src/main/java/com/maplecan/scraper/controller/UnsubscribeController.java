package com.maplecan.scraper.controller;

import com.maplecan.scraper.service.UnsubscribeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Value;

import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/public/unsubscribe")
@Slf4j
public class UnsubscribeController {
    
    @Autowired
    private UnsubscribeService unsubscribeService;

    @Value("${frontend.url}")
    private String frontendUrl;
    
    /**
     * Validate unsubscribe token and return email for frontend display
     */
    @GetMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateToken(@RequestParam String token) {
        try {
            Optional<String> emailOpt = unsubscribeService.validateAndExtractEmail(token);
            
            if (emailOpt.isPresent()) {
                String email = emailOpt.get();
                boolean isSubscribed = unsubscribeService.isUserSubscribed(email);
                
                return ResponseEntity.ok(Map.of(
                    "valid", true,
                    "email", email,
                    "isSubscribed", isSubscribed
                ));
            } else {
                return ResponseEntity.ok(Map.of(
                    "valid", false,
                    "message", "Invalid or expired unsubscribe link"
                ));
            }
        } catch (Exception e) {
            log.error("Error validating unsubscribe token", e);
            return ResponseEntity.ok(Map.of(
                "valid", false,
                "message", "Invalid unsubscribe link"
            ));
        }
    }
    
    /**
     * Process unsubscribe request
     */
    @PostMapping("/confirm")
    public ResponseEntity<Map<String, Object>> confirmUnsubscribe(
            @RequestParam String token,
            @RequestParam(required = false, defaultValue = "User requested") String reason) {
        
        try {
            Optional<String> emailOpt = unsubscribeService.validateAndExtractEmail(token);
            
            if (emailOpt.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Invalid or expired unsubscribe link"
                ));
            }
            
            String email = emailOpt.get();
            boolean success = unsubscribeService.unsubscribeUser(email, reason);
            
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Successfully unsubscribed from email notifications",
                    "email", email
                ));
            } else {
                return ResponseEntity.internalServerError().body(Map.of(
                    "success", false,
                    "message", "Failed to process unsubscribe request"
                ));
            }
            
        } catch (Exception e) {
            log.error("Error processing unsubscribe request", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "An error occurred while processing your request"
            ));
        }
    }
    
    /**
     * Generate unsubscribe token for testing purposes (development only)
     */
    @PostMapping("/generate-token")
    public ResponseEntity<Map<String, String>> generateToken(@RequestParam String email) {
        // This endpoint should be removed or secured in production
        try {
            String token = unsubscribeService.generateUnsubscribeToken(email);
            return ResponseEntity.ok(Map.of(
                "token", token,
                "email", email,
                "unsubscribeUrl", frontendUrl + "/unsubscribe?token=" + token
            ));
        } catch (Exception e) {
            log.error("Error generating unsubscribe token", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "error", "Failed to generate token"
            ));
        }
    }
}
