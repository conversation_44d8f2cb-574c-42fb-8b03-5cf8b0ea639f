package com.maplecan.scraper.controller;

import com.maplecan.scraper.model.ItemHistory;
import com.maplecan.scraper.model.PriceChange;
import com.maplecan.scraper.repository.ItemHistoryRepository;
import com.maplecan.scraper.repository.PriceChangeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/price")
public class PriceController {

    @Autowired
    private ItemHistoryRepository itemHistoryRepository;

    @Autowired
    private PriceChangeRepository priceChangeRepository;

    /**
     * Fetch item history by URL and optional date range.
     * If dates are not provided, defaults to last 4 months.
     *
     * @param url       The URL of the item to fetch history for.
     * @param startDate Optional start date (default is 4 months before current date).
     * @param endDate   Optional end date (default is current date).
     * @return List of ItemHistory within the specified range for the given URL.
     */
    @GetMapping("/history")
    public List<ItemHistory> getItemHistoryByUrlAndDates(
            @RequestParam String url,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {

        // Default end date to current date if not provided
        if (endDate == null) {
            endDate = LocalDateTime.now();
        }

        // Default start date to 4 months before the end date if not provided
        if (startDate == null) {
            startDate = endDate.minusMonths(4);
        }

        return itemHistoryRepository.findByUrlAndDateTimeBetween(url, startDate, endDate);
    }

    @GetMapping("/change")
    public List<PriceChange> getPriceByUrlAndDates(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {

        // Default end date to current date if not provided
        if (endDate == null) {
            endDate = LocalDateTime.now();
        }

        // Default start date to 4 months before the end date if not provided
        if (startDate == null) {
            startDate = endDate.minusDays(7);
        }
        return priceChangeRepository.findByCreatedDateBetweenOrderByCreatedDateDesc(startDate, endDate);
    }
}
