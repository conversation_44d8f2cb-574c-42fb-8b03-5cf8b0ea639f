package com.maplecan.scraper.controller;

import com.maplecan.scraper.service.WalmartApiService;
import com.maplecan.scraper.util.WalmartApiSignatureGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Controller for testing Walmart API integration
 */
@Slf4j
@RestController
@RequestMapping("/api/walmart")
public class WalmartApiController {

    private final WalmartApiSignatureGenerator signatureGenerator;

    @Autowired
    private WalmartApiService walmartApiService;

    public WalmartApiController() {
        this.signatureGenerator = new WalmartApiSignatureGenerator();
    }

    /**
     * Endpoint to generate Walmart API headers for testing
     * @return Map containing all required Walmart API headers
     */
    @GetMapping("/headers")
    public ResponseEntity<Map<String, String>> generateHeaders() {
        try {
            log.info("Generating Walmart API headers...");
            Map<String, String> headers = signatureGenerator.generateWalmartApiHeaders();
            log.info("Successfully generated Walmart API headers");
            return ResponseEntity.ok(headers);
        } catch (Exception e) {
            log.error("Error generating Walmart API headers: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Test endpoint to verify the headers work with a simple Walmart API call
     * You can expand this to make actual API calls to Walmart
     */
    @GetMapping("/test")
    public ResponseEntity<String> testWalmartApi() {
        try {
            Map<String, String> headers = signatureGenerator.generateWalmartApiHeaders();

            // Here you would make an actual call to Walmart API
            // For now, just return the headers as a formatted string
            StringBuilder response = new StringBuilder();
            response.append("Walmart API Headers Generated Successfully:\n\n");
            headers.forEach((key, value) ->
                response.append(key).append(": ").append(value).append("\n"));

            return ResponseEntity.ok(response.toString());
        } catch (Exception e) {
            log.error("Error in Walmart API test: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Error generating headers: " + e.getMessage());
        }
    }

    /**
     * Lookup product by item ID
     * Example: /api/walmart/lookup/item/4837473?zipCode=95054
     */
    @GetMapping("/lookup/item/{itemId}")
    public ResponseEntity<String> lookupByItemId(
            @PathVariable String itemId,
            @RequestParam(required = false) String zipCode) {
        try {
            log.info("Looking up Walmart product by itemId: {}", itemId);
            String result = walmartApiService.lookupByItemId(itemId, zipCode);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error looking up product by itemId {}: {}", itemId, e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Error looking up product: " + e.getMessage());
        }
    }

    /**
     * Lookup product by UPC
     * Example: /api/walmart/lookup/upc?upc=035000521019&zipCode=95054
     */
    @GetMapping("/lookup/upc")
    public ResponseEntity<String> lookupByUpc(
            @RequestParam String upc,
            @RequestParam(required = false) String zipCode) {
        try {
            log.info("Looking up Walmart product by UPC: {}", upc);
            String result = walmartApiService.lookupByUpc(upc, zipCode);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error looking up product by UPC {}: {}", upc, e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Error looking up product: " + e.getMessage());
        }
    }

    /**
     * Lookup product by GTIN
     * Example: /api/walmart/lookup/gtin?gtin=00039217119199&zipCode=95054
     */
    @GetMapping("/lookup/gtin")
    public ResponseEntity<String> lookupByGtin(
            @RequestParam String gtin,
            @RequestParam(required = false) String zipCode) {
        try {
            log.info("Looking up Walmart product by GTIN: {}", gtin);
            String result = walmartApiService.lookupByGtin(gtin, zipCode);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error looking up product by GTIN {}: {}", gtin, e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Error looking up product: " + e.getMessage());
        }
    }

    /**
     * Lookup multiple products by item IDs (up to 20 items)
     * Example: /api/walmart/lookup/items?ids=12417832,19336123&zipCode=95054
     */
    @GetMapping("/lookup/items")
    public ResponseEntity<String> lookupByItemIds(
            @RequestParam String ids,
            @RequestParam(required = false) String zipCode) {
        try {
            log.info("Looking up Walmart products by itemIds: {}", ids);
            String result = walmartApiService.lookupByItemIds(ids, zipCode);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error looking up products by itemIds {}: {}", ids, e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Error looking up products: " + e.getMessage());
        }
    }
}
