package com.maplecan.scraper.controller;

import com.maplecan.scraper.model.Item;
import com.maplecan.scraper.model.ItemRequest;
import com.maplecan.scraper.repository.ItemRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/item")
@Slf4j
public class ItemController {

    private static final int MAX_ITEMS_PER_USER = 5;

    @Autowired
    private ItemRepository itemRepository;

    /**
     * Adds a new item for a given user.
     * Limits the number of items to 5 per user.
     * If the user already has 5 items, it returns a 400 Bad Request with a clear message.
     * If an item with the same URL already exists for the user, it returns a 409 Conflict.
     *
     * @param itemRequest The request body containing item details and user email.
     * @return ResponseEntity with appropriate status and message.
     */
    @PostMapping()
    public ResponseEntity<String> addItem(@RequestBody ItemRequest itemRequest) {
        log.info("Adding item with URL: {} for user: {}", itemRequest.getUrl(), itemRequest.getEmail());
        String email2 = (String) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        itemRequest.setEmail(email2);

        // Check if the user has exceeded the maximum number of items
        List<Item> userItems = itemRepository.findByUserEmailAdded(itemRequest.getEmail());
        if (userItems.size() >= MAX_ITEMS_PER_USER) {
            log.warn("User {} has reached the maximum item limit of {}", itemRequest.getEmail(), MAX_ITEMS_PER_USER);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("You have reached the maximum limit of " + MAX_ITEMS_PER_USER + " items.");
        }

        // Check if an item with the same URL already exists globally (for any user)
        boolean urlExistsGlobally = itemRepository.existsByUrl(itemRequest.getUrl());
        if (urlExistsGlobally) {
            log.warn("Item with URL: {} already exists globally", itemRequest.getUrl());
            return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body("This item already exists.");
        }

        // Create a new Item object and save it
        Item item = Item.builder()
                .url(itemRequest.getUrl())
                .userEmailAdded(itemRequest.getEmail())
                .createdTimestamp(LocalDateTime.now())
                .build();

        itemRepository.save(item);
        log.info("Item with URL: {} successfully added for user: {}", itemRequest.getUrl(), itemRequest.getEmail());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body("Item successfully added.");
    }

}
