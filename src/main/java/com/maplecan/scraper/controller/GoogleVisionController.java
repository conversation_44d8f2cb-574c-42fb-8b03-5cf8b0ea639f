package com.maplecan.scraper.controller;

import com.maplecan.scraper.model.ReceiptItem;
import com.maplecan.scraper.service.GoogleVisionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/vision")
public class GoogleVisionController {

    @Autowired
    private GoogleVisionService googleVisionService;

    @GetMapping("/analyze")
    public ResponseEntity<?> analyzeImage(@RequestParam String imageUrl) {
        try {
            Map<String, Object> receiptData = googleVisionService.processReceipt(imageUrl);
            return ResponseEntity.ok(receiptData);
        } catch (IOException e) {
            return ResponseEntity.status(500).body("Error processing receipt: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }
}
