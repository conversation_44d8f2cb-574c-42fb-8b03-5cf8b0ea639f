package com.maplecan.scraper.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Factory for determining which screenshot scraper to use for a given URL
 */
@Slf4j
@Service
public class ScreenshotFactory {

    private final List<ScreenshotScraper> screenshotScrapers;

    public ScreenshotFactory(GenericScreenshotScraper genericScreenshotScraper) {
        // Initialize with available screenshot scrapers
        // Generic scraper should be last as it's the fallback
        this.screenshotScrapers = List.of(
                genericScreenshotScraper
        );
    }

    /**
     * Gets the appropriate screenshot scraper for the given URL
     * @param url The URL to capture
     * @return ScreenshotScraper that can handle the URL
     * @throws IllegalArgumentException if no scraper supports the URL
     */
    public ScreenshotScraper getScreenshotScraper(String url) {
        log.debug("Finding screenshot scraper for URL: {}", url);
        
        for (ScreenshotScraper scraper : screenshotScrapers) {
            if (scraper.supports(url)) {
                log.debug("Selected screenshot scraper: {}", scraper.getClass().getSimpleName());
                return scraper;
            }
        }
        
        throw new IllegalArgumentException("No screenshot scraper available for URL: " + url);
    }

    /**
     * Checks if any scraper supports the given URL
     * @param url The URL to check
     * @return true if a scraper is available for this URL
     */
    public boolean isSupported(String url) {
        return screenshotScrapers.stream()
                .anyMatch(scraper -> scraper.supports(url));
    }
}
