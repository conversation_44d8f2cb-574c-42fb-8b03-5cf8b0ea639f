package com.maplecan.scraper.service;

import com.maplecan.scraper.model.ScrapedData;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class WayfairCanadaScraper implements Scraper {

    private final RestTemplate restTemplate;

    @Value("${scrapingbee.api.key}")
    private String apiKey;

    public WayfairCanadaScraper(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Override
    public ScrapedData fetch(String url, String province) {
        String apiUrl = "https://app.scrapingbee.com/api/v1/";
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(apiUrl)
                .queryParam("api_key", apiKey)
                .queryParam("render_js", true) // Render JavaScript to load dynamic content
                .queryParam("premium_proxy", true)
                .queryParam("url", url)
                .queryParam("block_resources", true)
                .queryParam("country_code", "ca")
                .queryParam("stealth_proxy", true);

        try {
            String response = restTemplate.getForObject(uriBuilder.toUriString(), String.class);

            // Extract the sale price using regex
            Double price = extractPrice(response);
            Double discountPrice = extractDiscount(response);
            Double actualPrice = discountPrice.equals(0.0) ? price : discountPrice;
            String imageUrl = extractImageUrl(response);

            ScrapedData data = ScrapedData.builder()
                    .url(url)
                    .imageUrl(imageUrl)
                    .price(actualPrice)
                    .province(province)
                    .build();

            return data;
        } catch (org.springframework.web.client.HttpClientErrorException.NotFound ex) {
            // Handle 404 - item not found
            return ScrapedData.builder()
                    .url(url)
                    .province(province)
                    .isAvailable(false)
                    .build();
        }
    }

    @Override
    public ScrapedData fetchByItemNumber(String itemNumber) {
        return null;
    }

    private Double extractPrice(String htmlContent) {
        // Regex pattern to match the Wayfair price
        String regex = "data-name-id=\"PriceDisplay\"[^>]*>\\$([\\d,]+\\.\\d{2})<";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(htmlContent);
        if (matcher.find()) {
            String price = matcher.group(1).replace(",", ""); // Remove commas
            return Double.parseDouble(price);
        }
        return 0.0; // Default price if not found
    }

    private Double extractDiscount(String htmlContent) {
        // Regex pattern to match the JSON string containing salePrice, consumerPrice, and listPrice
        String regex = "\"salePrice\":(\\d+(?:\\.\\d+)?),\"consumerPrice\":(\\d+(?:\\.\\d+)?),\"listPrice\":(\\d+(?:\\.\\d+)?)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(htmlContent);

        if (matcher.find()) {
            return Double.parseDouble(matcher.group(1)); // Extract and return the salePrice
        }
        return 0.0; // Default price if not found
    }

    private String extractImageUrl(String htmlContent) {
        // Regex to match <div data-enzyme-id="FluidImage-wrapper" and extract the image URL from the <img> inside
        String regex = "<div[^>]*data-enzyme-id=\"FluidImage-wrapper\"[^>]*>.*?<img[^>]*src=\"(https?://[^\"]+)\"";
        Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);
        Matcher matcher = pattern.matcher(htmlContent);

        if (matcher.find()) {
            return matcher.group(1); // Extract the first matched image URL within the specific <div>
        }
        return null; // Return null if no match is found
    }

}
