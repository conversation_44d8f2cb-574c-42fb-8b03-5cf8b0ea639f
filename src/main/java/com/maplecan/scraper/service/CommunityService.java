package com.maplecan.scraper.service;

import com.maplecan.scraper.configuration.UserPrincipal;
import com.maplecan.scraper.dto.*;
import com.maplecan.scraper.model.*;
import com.maplecan.scraper.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CommunityService {

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private CommunityPostRepository postRepository;

    @Autowired
    private CommunityCommentRepository commentRepository;

    // User Profile Management
    public UserProfile createUserProfile(CreateUserProfileRequest request) {
        UserPrincipal principal = getCurrentUser();
        
        // Check if username already exists
        if (userProfileRepository.existsByUsername(request.getUsername())) {
            throw new IllegalArgumentException("Username already exists");
        }
        
        // Check if user already has a profile
        if (userProfileRepository.existsBySupabaseUserId(principal.getSupabaseUserId())) {
            throw new IllegalArgumentException("User profile already exists");
        }
        
        // Set country, default to CA if not provided or invalid
        String country = "CA"; // Default
        if (request.getCountry() != null &&
            (request.getCountry().equals("CA") || request.getCountry().equals("US"))) {
            country = request.getCountry();
        }

        UserProfile profile = UserProfile.builder()
                .supabaseUserId(principal.getSupabaseUserId())
                .username(request.getUsername())
                .firstName(request.getFirstName())
                .lastName(request.getLastName())
                .email(principal.getEmail())
                .country(country)
                .build();
        
        return userProfileRepository.save(profile);
    }

    public Optional<UserProfile> getUserProfile() {
        UserPrincipal principal = getCurrentUser();
        log.info("🔍 Service: getUserProfile for user: {}", principal.getSupabaseUserId());
        Optional<UserProfile> profile = userProfileRepository.findBySupabaseUserId(principal.getSupabaseUserId());
        log.info("🔍 Service: Profile lookup result: {}", profile.isPresent() ? "found" : "not found");
        return profile;
    }

    public boolean hasUserProfile() {
        UserPrincipal principal = getCurrentUser();
        return userProfileRepository.existsBySupabaseUserId(principal.getSupabaseUserId());
    }

    public UserProfile updateUserCountry(String country) {
        log.info("🔄 Service: updateUserCountry called with country: '{}'", country);

        UserPrincipal principal = getCurrentUser();
        log.info("🔍 Service: Current user: {}", principal.getSupabaseUserId());

        UserProfile profile = getUserProfileOrThrow();
        log.info("🔍 Service: Found user profile: {} (current country: {})", profile.getUsername(), profile.getCountry());

        // Validate country code
        if (!country.equals("CA") && !country.equals("US")) {
            log.warn("❌ Service: Invalid country code: '{}'. Must be 'CA' or 'US'", country);
            throw new IllegalArgumentException("Invalid country code. Must be 'CA' or 'US'");
        }

        log.info("✅ Service: Country validation passed, updating from '{}' to '{}'", profile.getCountry(), country);
        profile.setCountry(country);
        profile.setUpdatedAt(java.time.LocalDateTime.now());

        UserProfile savedProfile = userProfileRepository.save(profile);
        log.info("✅ Service: Profile saved successfully with country: '{}'", savedProfile.getCountry());
        return savedProfile;
    }

    // Post Management
    public PostResponse createPost(CreatePostRequest request) {
        UserPrincipal principal = getCurrentUser();
        UserProfile profile = getUserProfileOrThrow();
        
        CommunityPost post = CommunityPost.builder()
                .authorId(principal.getSupabaseUserId())
                .title(request.getTitle())
                .content(request.getContent())
                .productUrl(request.getProductUrl())
                .productName(request.getProductName())
                .productImage(request.getProductImage())
                .originalPrice(request.getOriginalPrice())
                .currentPrice(request.getCurrentPrice())
                .store(request.getStore())
                .category(request.getCategory())
                .upvotedBy(new ArrayList<>())
                .downvotedBy(new ArrayList<>())
                .build();
        
        CommunityPost savedPost = postRepository.save(post);
        
        // Update user stats
        profile.setTotalPosts(profile.getTotalPosts() + 1);
        userProfileRepository.save(profile);
        
        return PostResponse.fromEntity(savedPost, profile.getUsername(), false, false);
    }

    public Page<PostResponse> getPosts(int page, int size, String store, String category, String sort) {
        Pageable pageable = createPageable(page, size, sort);
        Page<CommunityPost> posts;
        
        if (store != null && !store.isEmpty()) {
            posts = postRepository.findByStoreAndIsActiveTrueOrderByCreatedAtDesc(store, pageable);
        } else if (category != null && !category.isEmpty()) {
            posts = postRepository.findByCategoryAndIsActiveTrueOrderByCreatedAtDesc(category, pageable);
        } else {
            posts = postRepository.findByIsActiveTrueOrderByCreatedAtDesc(pageable);
        }
        
        return posts.map(this::convertToPostResponse);
    }

    public PostResponse getPost(String postId) {
        CommunityPost post = postRepository.findById(postId)
                .orElseThrow(() -> new IllegalArgumentException("Post not found"));
        
        // Increment view count
        post.setViewCount(post.getViewCount() + 1);
        postRepository.save(post);
        
        return convertToPostResponse(post);
    }

    // Voting
    public PostResponse votePost(String postId, boolean isUpvote) {
        UserPrincipal principal = getCurrentUser();
        CommunityPost post = postRepository.findById(postId)
                .orElseThrow(() -> new IllegalArgumentException("Post not found"));
        
        String userId = principal.getSupabaseUserId();
        List<String> upvotedBy = post.getUpvotedBy() != null ? post.getUpvotedBy() : new ArrayList<>();
        List<String> downvotedBy = post.getDownvotedBy() != null ? post.getDownvotedBy() : new ArrayList<>();
        
        // Remove existing votes
        upvotedBy.remove(userId);
        downvotedBy.remove(userId);
        
        // Add new vote
        if (isUpvote) {
            upvotedBy.add(userId);
        } else {
            downvotedBy.add(userId);
        }
        
        post.setUpvotedBy(upvotedBy);
        post.setDownvotedBy(downvotedBy);
        post.setUpvotes(upvotedBy.size());
        post.setDownvotes(downvotedBy.size());
        
        CommunityPost savedPost = postRepository.save(post);
        return convertToPostResponse(savedPost);
    }

    // Comment Management
    public CommentResponse createComment(String postId, CreateCommentRequest request) {
        UserPrincipal principal = getCurrentUser();
        UserProfile profile = getUserProfileOrThrow();

        // Verify post exists
        CommunityPost post = postRepository.findById(postId)
                .orElseThrow(() -> new IllegalArgumentException("Post not found"));

        CommunityComment comment = CommunityComment.builder()
                .postId(postId)
                .authorId(principal.getSupabaseUserId())
                .content(request.getContent())
                .parentCommentId(request.getParentCommentId())
                .depth(request.getParentCommentId() != null ? 1 : 0)
                .upvotedBy(new ArrayList<>())
                .downvotedBy(new ArrayList<>())
                .build();

        CommunityComment savedComment = commentRepository.save(comment);

        // Update post comment count
        post.setCommentCount(post.getCommentCount() + 1);
        postRepository.save(post);

        // Update user stats
        profile.setTotalComments(profile.getTotalComments() + 1);
        userProfileRepository.save(profile);

        return CommentResponse.fromEntity(savedComment, profile.getUsername(), false, false);
    }

    public List<CommentResponse> getComments(String postId) {
        List<CommunityComment> comments = commentRepository.findByPostIdAndIsActiveTrueOrderByCreatedAtAsc(postId);
        return comments.stream()
                .map(this::convertToCommentResponse)
                .collect(Collectors.toList());
    }

    public CommentResponse voteComment(String commentId, boolean isUpvote) {
        UserPrincipal principal = getCurrentUser();
        CommunityComment comment = commentRepository.findById(commentId)
                .orElseThrow(() -> new IllegalArgumentException("Comment not found"));

        String userId = principal.getSupabaseUserId();
        List<String> upvotedBy = comment.getUpvotedBy() != null ? comment.getUpvotedBy() : new ArrayList<>();
        List<String> downvotedBy = comment.getDownvotedBy() != null ? comment.getDownvotedBy() : new ArrayList<>();

        // Remove existing votes
        upvotedBy.remove(userId);
        downvotedBy.remove(userId);

        // Add new vote
        if (isUpvote) {
            upvotedBy.add(userId);
        } else {
            downvotedBy.add(userId);
        }

        comment.setUpvotedBy(upvotedBy);
        comment.setDownvotedBy(downvotedBy);
        comment.setUpvotes(upvotedBy.size());
        comment.setDownvotes(downvotedBy.size());

        CommunityComment savedComment = commentRepository.save(comment);
        return convertToCommentResponse(savedComment);
    }

    // Helper methods
    private UserPrincipal getCurrentUser() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (!(principal instanceof UserPrincipal)) {
            throw new IllegalStateException("User not authenticated");
        }
        return (UserPrincipal) principal;
    }

    private UserProfile getUserProfileOrThrow() {
        log.info("🔍 Service: getUserProfileOrThrow called");
        Optional<UserProfile> profileOpt = getUserProfile();
        log.info("🔍 Service: getUserProfile returned: {}", profileOpt.isPresent() ? "profile found" : "no profile");

        return profileOpt.orElseThrow(() -> {
            log.warn("❌ Service: User profile not found - throwing IllegalStateException");
            return new IllegalStateException("User profile not found. Please create a username first.");
        });
    }

    private PostResponse convertToPostResponse(CommunityPost post) {
        UserProfile author = userProfileRepository.findBySupabaseUserId(post.getAuthorId())
                .orElse(null);
        String authorUsername = author != null ? author.getUsername() : "Unknown User";
        
        UserPrincipal currentUser = null;
        try {
            currentUser = getCurrentUser();
        } catch (Exception e) {
            // User not authenticated, return without vote status
        }
        
        boolean hasUpvoted = false;
        boolean hasDownvoted = false;
        
        if (currentUser != null) {
            String userId = currentUser.getSupabaseUserId();
            hasUpvoted = post.getUpvotedBy() != null && post.getUpvotedBy().contains(userId);
            hasDownvoted = post.getDownvotedBy() != null && post.getDownvotedBy().contains(userId);
        }
        
        return PostResponse.fromEntity(post, authorUsername, hasUpvoted, hasDownvoted);
    }

    private CommentResponse convertToCommentResponse(CommunityComment comment) {
        UserProfile author = userProfileRepository.findBySupabaseUserId(comment.getAuthorId())
                .orElse(null);
        String authorUsername = author != null ? author.getUsername() : "Unknown User";

        UserPrincipal currentUser = null;
        try {
            currentUser = getCurrentUser();
        } catch (Exception e) {
            // User not authenticated, return without vote status
        }

        boolean hasUpvoted = false;
        boolean hasDownvoted = false;

        if (currentUser != null) {
            String userId = currentUser.getSupabaseUserId();
            hasUpvoted = comment.getUpvotedBy() != null && comment.getUpvotedBy().contains(userId);
            hasDownvoted = comment.getDownvotedBy() != null && comment.getDownvotedBy().contains(userId);
        }

        return CommentResponse.fromEntity(comment, authorUsername, hasUpvoted, hasDownvoted);
    }

    private Pageable createPageable(int page, int size, String sort) {
        Sort sortObj = Sort.by(Sort.Direction.DESC, "createdAt");
        
        if ("hot".equals(sort)) {
            sortObj = Sort.by(Sort.Direction.DESC, "upvotes", "createdAt");
        } else if ("top".equals(sort)) {
            sortObj = Sort.by(Sort.Direction.DESC, "upvotes");
        }
        
        return PageRequest.of(page, size, sortObj);
    }
}
