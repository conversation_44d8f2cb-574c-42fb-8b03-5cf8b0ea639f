package com.maplecan.scraper.service;

import com.maplecan.scraper.model.ImageComparisonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URL;
import java.util.*;
import java.util.List;

@Slf4j
@Service
public class ImageComparisonService {

    /**
     * Compare two images and return detailed comparison results
     */
    public ImageComparisonResult compareImages(String imageUrl1, String imageUrl2) throws IOException {
        log.info("Comparing images: {} vs {}", imageUrl1, imageUrl2);
        
        BufferedImage image1 = loadImageFromUrl(imageUrl1);
        BufferedImage image2 = loadImageFromUrl(imageUrl2);
        
        if (image1 == null || image2 == null) {
            throw new IOException("Failed to load one or both images");
        }
        
        // Resize images to same dimensions for comparison
        Dimension targetSize = getOptimalComparisonSize(image1, image2);
        BufferedImage resized1 = resizeImage(image1, targetSize);
        BufferedImage resized2 = resizeImage(image2, targetSize);
        
        // Perform multiple comparison techniques
        double pixelSimilarity = calculatePixelSimilarity(resized1, resized2);
        double structuralSimilarity = calculateStructuralSimilarity(resized1, resized2);
        double histogramSimilarity = calculateHistogramSimilarity(resized1, resized2);
        double perceptualHash = calculatePerceptualHashSimilarity(resized1, resized2);
        
        // Calculate overall similarity score (weighted average)
        double overallSimilarity = (pixelSimilarity * 0.3) + 
                                 (structuralSimilarity * 0.3) + 
                                 (histogramSimilarity * 0.2) + 
                                 (perceptualHash * 0.2);
        
        // Generate difference map
        BufferedImage differenceMap = generateDifferenceMap(resized1, resized2);
        
        return ImageComparisonResult.builder()
                .overallSimilarity(overallSimilarity)
                .pixelSimilarity(pixelSimilarity)
                .structuralSimilarity(structuralSimilarity)
                .histogramSimilarity(histogramSimilarity)
                .perceptualHashSimilarity(perceptualHash)
                .hasDifferences(overallSimilarity < 0.95) // 95% threshold
                .significantDifferences(overallSimilarity < 0.85) // 85% threshold
                .differenceMap(differenceMap)
                .image1Dimensions(new Dimension(image1.getWidth(), image1.getHeight()))
                .image2Dimensions(new Dimension(image2.getWidth(), image2.getHeight()))
                .comparisonDimensions(targetSize)
                .build();
    }
    
    /**
     * Load image from URL with error handling
     */
    private BufferedImage loadImageFromUrl(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            BufferedImage image = ImageIO.read(url);
            if (image == null) {
                log.error("ImageIO.read returned null for URL: {}", imageUrl);
                return null;
            }
            log.debug("Successfully loaded image: {}x{} from {}", 
                     image.getWidth(), image.getHeight(), imageUrl);
            return image;
        } catch (Exception e) {
            log.error("Failed to load image from URL: {}", imageUrl, e);
            return null;
        }
    }
    
    /**
     * Determine optimal size for comparison (smaller of the two, but not too small)
     */
    private Dimension getOptimalComparisonSize(BufferedImage img1, BufferedImage img2) {
        int width = Math.min(img1.getWidth(), img2.getWidth());
        int height = Math.min(img1.getHeight(), img2.getHeight());
        
        // Ensure minimum size for meaningful comparison
        width = Math.max(width, 200);
        height = Math.max(height, 200);
        
        // Cap maximum size for performance
        width = Math.min(width, 1000);
        height = Math.min(height, 1000);
        
        return new Dimension(width, height);
    }
    
    /**
     * Resize image to target dimensions
     */
    private BufferedImage resizeImage(BufferedImage original, Dimension targetSize) {
        BufferedImage resized = new BufferedImage(
            targetSize.width, targetSize.height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = resized.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(original, 0, 0, targetSize.width, targetSize.height, null);
        g2d.dispose();
        return resized;
    }
    
    /**
     * Calculate pixel-by-pixel similarity
     */
    private double calculatePixelSimilarity(BufferedImage img1, BufferedImage img2) {
        int width = img1.getWidth();
        int height = img1.getHeight();
        long totalPixels = (long) width * height;
        long matchingPixels = 0;
        
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                Color color1 = new Color(img1.getRGB(x, y));
                Color color2 = new Color(img2.getRGB(x, y));
                
                // Calculate color distance (Euclidean distance in RGB space)
                double distance = Math.sqrt(
                    Math.pow(color1.getRed() - color2.getRed(), 2) +
                    Math.pow(color1.getGreen() - color2.getGreen(), 2) +
                    Math.pow(color1.getBlue() - color2.getBlue(), 2)
                );
                
                // Consider pixels similar if distance is less than threshold
                if (distance < 30) { // Threshold for "similar" colors
                    matchingPixels++;
                }
            }
        }
        
        return (double) matchingPixels / totalPixels;
    }
    
    /**
     * Calculate structural similarity using a simplified SSIM approach
     */
    private double calculateStructuralSimilarity(BufferedImage img1, BufferedImage img2) {
        // Convert to grayscale for structural analysis
        BufferedImage gray1 = convertToGrayscale(img1);
        BufferedImage gray2 = convertToGrayscale(img2);
        
        int width = gray1.getWidth();
        int height = gray1.getHeight();
        
        // Calculate mean intensities
        double mean1 = calculateMeanIntensity(gray1);
        double mean2 = calculateMeanIntensity(gray2);
        
        // Calculate variances and covariance
        double variance1 = 0, variance2 = 0, covariance = 0;
        int totalPixels = width * height;
        
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                double intensity1 = new Color(gray1.getRGB(x, y)).getRed();
                double intensity2 = new Color(gray2.getRGB(x, y)).getRed();
                
                variance1 += Math.pow(intensity1 - mean1, 2);
                variance2 += Math.pow(intensity2 - mean2, 2);
                covariance += (intensity1 - mean1) * (intensity2 - mean2);
            }
        }
        
        variance1 /= totalPixels;
        variance2 /= totalPixels;
        covariance /= totalPixels;
        
        // Simplified SSIM calculation
        double c1 = 6.5025; // (0.01 * 255)^2
        double c2 = 58.5225; // (0.03 * 255)^2
        
        double numerator = (2 * mean1 * mean2 + c1) * (2 * covariance + c2);
        double denominator = (mean1 * mean1 + mean2 * mean2 + c1) * (variance1 + variance2 + c2);
        
        return numerator / denominator;
    }
    
    /**
     * Calculate histogram similarity
     */
    private double calculateHistogramSimilarity(BufferedImage img1, BufferedImage img2) {
        int[] hist1 = calculateHistogram(img1);
        int[] hist2 = calculateHistogram(img2);
        
        // Calculate correlation coefficient between histograms
        return calculateCorrelation(hist1, hist2);
    }
    
    /**
     * Calculate perceptual hash similarity (simplified pHash)
     */
    private double calculatePerceptualHashSimilarity(BufferedImage img1, BufferedImage img2) {
        String hash1 = calculatePerceptualHash(img1);
        String hash2 = calculatePerceptualHash(img2);
        
        // Calculate Hamming distance
        int differences = 0;
        for (int i = 0; i < Math.min(hash1.length(), hash2.length()); i++) {
            if (hash1.charAt(i) != hash2.charAt(i)) {
                differences++;
            }
        }
        
        return 1.0 - ((double) differences / hash1.length());
    }
    
    /**
     * Generate a visual difference map
     */
    private BufferedImage generateDifferenceMap(BufferedImage img1, BufferedImage img2) {
        int width = img1.getWidth();
        int height = img1.getHeight();
        BufferedImage diffMap = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                Color color1 = new Color(img1.getRGB(x, y));
                Color color2 = new Color(img2.getRGB(x, y));
                
                // Calculate absolute difference
                int redDiff = Math.abs(color1.getRed() - color2.getRed());
                int greenDiff = Math.abs(color1.getGreen() - color2.getGreen());
                int blueDiff = Math.abs(color1.getBlue() - color2.getBlue());
                
                // Amplify differences for visibility
                redDiff = Math.min(255, redDiff * 3);
                greenDiff = Math.min(255, greenDiff * 3);
                blueDiff = Math.min(255, blueDiff * 3);
                
                Color diffColor = new Color(redDiff, greenDiff, blueDiff);
                diffMap.setRGB(x, y, diffColor.getRGB());
            }
        }
        
        return diffMap;
    }
    
    // Helper methods
    private BufferedImage convertToGrayscale(BufferedImage original) {
        BufferedImage grayscale = new BufferedImage(
            original.getWidth(), original.getHeight(), BufferedImage.TYPE_BYTE_GRAY);
        Graphics2D g2d = grayscale.createGraphics();
        g2d.drawImage(original, 0, 0, null);
        g2d.dispose();
        return grayscale;
    }
    
    private double calculateMeanIntensity(BufferedImage image) {
        long sum = 0;
        int width = image.getWidth();
        int height = image.getHeight();
        
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                sum += new Color(image.getRGB(x, y)).getRed();
            }
        }
        
        return (double) sum / (width * height);
    }
    
    private int[] calculateHistogram(BufferedImage image) {
        int[] histogram = new int[256];
        int width = image.getWidth();
        int height = image.getHeight();
        
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                Color color = new Color(image.getRGB(x, y));
                int gray = (int) (0.299 * color.getRed() + 0.587 * color.getGreen() + 0.114 * color.getBlue());
                histogram[gray]++;
            }
        }
        
        return histogram;
    }
    
    private double calculateCorrelation(int[] hist1, int[] hist2) {
        double mean1 = Arrays.stream(hist1).average().orElse(0);
        double mean2 = Arrays.stream(hist2).average().orElse(0);
        
        double numerator = 0, sumSq1 = 0, sumSq2 = 0;
        
        for (int i = 0; i < hist1.length; i++) {
            double diff1 = hist1[i] - mean1;
            double diff2 = hist2[i] - mean2;
            numerator += diff1 * diff2;
            sumSq1 += diff1 * diff1;
            sumSq2 += diff2 * diff2;
        }
        
        double denominator = Math.sqrt(sumSq1 * sumSq2);
        return denominator == 0 ? 0 : numerator / denominator;
    }
    
    private String calculatePerceptualHash(BufferedImage image) {
        // Resize to 8x8 for pHash
        BufferedImage small = resizeImage(image, new Dimension(8, 8));
        BufferedImage gray = convertToGrayscale(small);
        
        // Calculate average pixel value
        double average = calculateMeanIntensity(gray);
        
        // Generate hash based on whether each pixel is above or below average
        StringBuilder hash = new StringBuilder();
        for (int y = 0; y < 8; y++) {
            for (int x = 0; x < 8; x++) {
                int pixel = new Color(gray.getRGB(x, y)).getRed();
                hash.append(pixel >= average ? '1' : '0');
            }
        }
        
        return hash.toString();
    }
}
