package com.maplecan.scraper.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maplecan.scraper.model.ScrapedData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * Walmart scraper implementation that integrates with the existing scraper architecture
 */
@Slf4j
@Service
public class WalmartScraper implements Scraper {

    @Autowired
    private WalmartApiService walmartApiService;
    
    @Autowired
    private WalmartUrlParser walmartUrlParser;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public ScrapedData fetch(String url, String province) {
        try {
            log.info("Fetching Walmart product from URL: {}", url);
            
            // Extract item ID from URL
            String itemId = walmartUrlParser.extractAndValidateItemId(url);
            if (itemId == null) {
                log.error("Could not extract valid item ID from Walmart URL: {}", url);
                return createUnavailableProduct(url, province);
            }
            
            log.info("Extracted item ID: {} from URL: {}", itemId, url);
            
            // Get zip code for province (you may want to customize this mapping)
            String zipCode = getZipCodeForProvince(province);
            
            // Call Walmart API
            String apiResponse = walmartApiService.lookupByItemId(itemId, zipCode);
            
            // Parse response and create ScrapedData
            return parseWalmartApiResponse(apiResponse, url, province);
            
        } catch (Exception e) {
            log.error("Error fetching Walmart product from URL {}: {}", url, e.getMessage(), e);
            return createUnavailableProduct(url, province);
        }
    }

    @Override
    public ScrapedData fetchByItemNumber(String itemNumber) {
        try {
            log.info("Fetching Walmart product by item number: {}", itemNumber);
            
            // For Walmart, item number IS the item ID
            if (!walmartUrlParser.isValidItemId(itemNumber)) {
                log.error("Invalid Walmart item ID: {}", itemNumber);
                return createUnavailableProductByItemNumber(itemNumber);
            }
            
            // Call Walmart API with default zip code
            String apiResponse = walmartApiService.lookupByItemId(itemNumber, "95054");
            
            // Parse response and create ScrapedData
            ScrapedData data = parseWalmartApiResponse(apiResponse, null, "US");
            
            // Set the item number with Walmart prefix for consistency
            data.setItemNumber("WALMART" + itemNumber);
            
            return data;
            
        } catch (Exception e) {
            log.error("Error fetching Walmart product by item number {}: {}", itemNumber, e.getMessage(), e);
            return createUnavailableProductByItemNumber(itemNumber);
        }
    }

    /**
     * Parses Walmart API JSON response and creates ScrapedData object
     */
    private ScrapedData parseWalmartApiResponse(String jsonResponse, String originalUrl, String province) {
        try {
            JsonNode root = objectMapper.readTree(jsonResponse);
            
            // Extract product information
            String itemId = root.path("itemId").asText();
            String name = root.path("name").asText();
            double salePrice = root.path("salePrice").asDouble(0.0);
            String imageUrl = root.path("largeImage").asText();
            String upc = root.path("upc").asText();
            boolean availableOnline = root.path("availableOnline").asBoolean(true);
            String stock = root.path("stock").asText("Unknown");
            
            // Determine availability
            boolean isAvailable = availableOnline && !"Out of Stock".equalsIgnoreCase(stock);
            
            // Create product URL if not provided
            String productUrl = originalUrl;
            if (productUrl == null || productUrl.trim().isEmpty()) {
                productUrl = "https://www.walmart.com/ip/" + itemId;
            }
            
            return ScrapedData.builder()
                    .url(productUrl)
                    .productName(name)
                    .price(salePrice)
                    .imageUrl(imageUrl)
                    .itemNumber("WALMART" + itemId)
                    .province(province)
                    .isAvailable(isAvailable)
                    .currencyCode("USD")
                    .build();
                    
        } catch (Exception e) {
            log.error("Error parsing Walmart API response: {}", e.getMessage(), e);
            return createUnavailableProduct(originalUrl, province);
        }
    }

    /**
     * Maps province/state to appropriate zip code for pricing
     */
    private String getZipCodeForProvince(String province) {
        if (province == null || province.trim().isEmpty()) {
            return "95054"; // Default to San Francisco Bay Area
        }
        
        // Map common provinces/states to zip codes
        switch (province.toUpperCase()) {
            case "CA":
            case "CALIFORNIA":
                return "90210"; // Los Angeles
            case "NY":
            case "NEW YORK":
                return "10001"; // New York City
            case "TX":
            case "TEXAS":
                return "77001"; // Houston
            case "FL":
            case "FLORIDA":
                return "33101"; // Miami
            case "IL":
            case "ILLINOIS":
                return "60601"; // Chicago
            default:
                return "95054"; // Default
        }
    }

    /**
     * Creates unavailable product data for failed lookups
     */
    private ScrapedData createUnavailableProduct(String url, String province) {
        return ScrapedData.builder()
                .url(url)
                .province(province)
                .isAvailable(false)
                .currencyCode("USD")
                .build();
    }

    /**
     * Creates unavailable product data for failed item number lookups
     */
    private ScrapedData createUnavailableProductByItemNumber(String itemNumber) {
        return ScrapedData.builder()
                .url("https://www.walmart.com/ip/" + itemNumber)
                .itemNumber("WALMART" + itemNumber)
                .province("US")
                .isAvailable(false)
                .currencyCode("USD")
                .build();
    }
}
