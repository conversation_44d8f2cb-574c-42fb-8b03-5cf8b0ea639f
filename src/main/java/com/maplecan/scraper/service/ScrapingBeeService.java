package com.maplecan.scraper.service;

import com.maplecan.scraper.model.AddNewProductRequestByItemNumber;
import com.maplecan.scraper.model.Item;
import com.maplecan.scraper.repository.ScrapedDataRepository;
import com.maplecan.scraper.model.AddNewProductRequest;
import com.maplecan.scraper.model.ScrapedData;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class ScrapingBeeService {

    private final ScrapedDataRepository repository;
    private final ScraperFactory scraperFactory;
    private final ItemService itemService;

    @Value("${scrapingbee.api.key}")
    private String apiKey;

    public ScrapingBeeService(ScrapedDataRepository repository, ScraperFactory scraperFactory, ItemService itemService) {
        this.repository = repository;
        this.scraperFactory = scraperFactory;
        this.itemService = itemService;
    }

    public ScrapedData fetchUrlAndSave(String url, String province) {
        LocalDateTime timeNow = LocalDateTime.now();
        Scraper scraper = scraperFactory.getScraper(url);
        ScrapedData data = scraper.fetch(url, province);
        data.setCreatedTimestamp(timeNow);
        data.setUpdatedTimestamp(timeNow);
        data.setLastScanTime(timeNow);
        save(data);
        return data;
    }

    public ScrapedData fetchUrlAndSave(AddNewProductRequest request) {
        LocalDateTime timeNow = LocalDateTime.now();
        Scraper scraper = scraperFactory.getScraper(request.getUrl());
        ScrapedData data = scraper.fetch(request.getUrl(), request.getProvince());
        data.setEmail(request.getEmail());
        data.setProductName(request.getProductName());
        data.setUserPrice(request.getPrice());
        data.setProvince(request.getProvince());
        data.setCreatedTimestamp(timeNow);
        data.setUpdatedTimestamp(timeNow);
        data.setLastScanTime(timeNow);
        save(data);
        if (!itemService.existsByUrl(request.getUrl())) {
            itemService.save(
                    Item.builder()
                            .url(data.getUrl())
                            .imageUrl(data.getImageUrl())
                            .itemNumber(data.getItemNumber())
                            .latestPrice(data.getPrice())
                            .lastScanTime(timeNow)
                            .createdTimestamp(timeNow)
                            .updatedTimestamp(timeNow)
                            .isAvailable(data.getIsAvailable())
                            .build()
            );
        }
        return data;
    }

    public ScrapedData fetchItemNumberAndSave(AddNewProductRequestByItemNumber request) {
        LocalDateTime timeNow = LocalDateTime.now();
        Scraper scraper = scraperFactory.getScraperByStoreAndCountryCode(request.getStoreAndCountryCode());
        ScrapedData data = scraper.fetchByItemNumber(request.getItemNumber());
        data.setEmail(request.getEmail());
        data.setUserPrice(request.getPrice());
        data.setCreatedTimestamp(timeNow);
        data.setUpdatedTimestamp(timeNow);
        data.setLastScanTime(timeNow);
        String appendedItemNumber = request.getStoreAndCountryCode() + request.getItemNumber();
        save(data);
        if (!itemService.existsByUrl(data.getUrl())) {
            itemService.save(
                    Item.builder()
                            .url(data.getUrl())
                            .imageUrl(data.getImageUrl())
                            .itemNumber(appendedItemNumber)
                            .latestPrice(data.getPrice())
                            .lastScanTime(timeNow)
                            .createdTimestamp(timeNow)
                            .updatedTimestamp(timeNow)
                            .isAvailable(data.getIsAvailable())
                            .build()

            );
        }
        return data;
    }

    public void deleteById(String id) {
        ObjectId objectId = new ObjectId(id);
        repository.deleteById(objectId);
    }

    public Optional<ScrapedData> findById(String id) {
        return repository.findById(id);
    }

    public List<ScrapedData> getProductList(String email) {
        return repository.findByEmail(email);
    }

    public ScrapedData fetch(String url, String province) {
        Scraper scraper = scraperFactory.getScraper(url);
        return scraper.fetch(url, province);
    }

    public ScrapedData fetchByItemNumber(String itemNumber, String storeAndCountryCode) {
        Scraper scraper = scraperFactory.getScraperByStoreAndCountryCode(storeAndCountryCode);
        return scraper.fetchByItemNumber(itemNumber);
    }

    public void save(ScrapedData data) {
        repository.save(data);
    }
}
