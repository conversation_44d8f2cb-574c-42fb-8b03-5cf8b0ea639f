package com.maplecan.scraper.service;

import java.io.IOException;

/**
 * Interface for screenshot scraping implementations
 */
public interface ScreenshotScraper {
    
    /**
     * Captures a screenshot of the given URL
     * @param url The URL to capture
     * @return byte array containing the screenshot image data
     * @throws IOException if screenshot capture fails
     */
    byte[] captureScreenshot(String url) throws IOException;
    
    /**
     * Checks if this scraper supports the given URL
     * @param url The URL to check
     * @return true if this scraper can handle the URL
     */
    boolean supports(String url);
}
