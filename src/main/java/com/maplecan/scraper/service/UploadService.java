package com.maplecan.scraper.service;

import com.maplecan.scraper.model.UploadMetadata;
import com.maplecan.scraper.repository.UploadMetadataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class UploadService {

    @Autowired
    private UploadMetadataRepository uploadMetadataRepository;


    /**
     * Fetches all uploads for a given user ID.
     *
     * @param userId The ID of the user.
     * @return A list of UploadMetadata objects.
     */
    public List<UploadMetadata> getUploadsByUserId(String userId) {
        return uploadMetadataRepository.findByUserId(userId);
    }

    /**
     * Saves the uploaded file's metadata into MongoDB.
     *
     * @param userId   The ID of the user who uploaded the file.
     * @param filePath The file path in Supabase storage.
     * @param fileUrl  The public URL of the file.
     * @param fileType The MIME type of the file.
     */
    public void saveUploadMetadata(String userEmail, String userId, String filePath, String fileUrl, String fileType) {
        UploadMetadata metadata = new UploadMetadata();
        metadata.setUserId(userId);
        metadata.setUserEmail(userEmail);
        metadata.setFilePath(filePath);
        metadata.setFileUrl(fileUrl);
        metadata.setFileType(fileType);
        metadata.setUploadTime(LocalDateTime.now());
        metadata.setStatus("pending"); // Default status

        uploadMetadataRepository.save(metadata);
    }
}
