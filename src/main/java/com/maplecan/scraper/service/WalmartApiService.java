package com.maplecan.scraper.service;

import com.maplecan.scraper.util.WalmartApiSignatureGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

/**
 * Service for making calls to Walmart Affiliate API
 */
@Slf4j
@Service
public class WalmartApiService {

    private static final String WALMART_API_BASE_URL = "https://developer.api.walmart.com/api-proxy/service/affil/product/v2/items";
    
    @Autowired
    private RestTemplate restTemplate;
    
    private final WalmartApiSignatureGenerator signatureGenerator;

    public WalmartApiService() {
        this.signatureGenerator = new WalmartApiSignatureGenerator();
    }

    /**
     * Lookup product by item ID
     * @param itemId Walmart item ID
     * @param zipCode Optional zip code for location-specific pricing
     * @return JSON response from Walmart API
     */
    public String lookupByItemId(String itemId, String zipCode) {
        try {
            log.info("Looking up Walmart product by itemId: {}, zipCode: {}", itemId, zipCode);
            
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(WALMART_API_BASE_URL + "/" + itemId)
                    .queryParam("format", "json");
            
            if (zipCode != null && !zipCode.trim().isEmpty()) {
                uriBuilder.queryParam("zipCode", zipCode);
            }
            
            String url = uriBuilder.toUriString();
            log.info("Walmart API URL: {}", url);
            
            HttpHeaders headers = createWalmartHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            
            log.info("Walmart API response status: {}", response.getStatusCode());
            return response.getBody();
            
        } catch (Exception e) {
            log.error("Error calling Walmart API for itemId {}: {}", itemId, e.getMessage(), e);
            throw new RuntimeException("Failed to lookup Walmart product: " + e.getMessage(), e);
        }
    }

    /**
     * Lookup product by UPC
     * @param upc Universal Product Code
     * @param zipCode Optional zip code for location-specific pricing
     * @return JSON response from Walmart API
     */
    public String lookupByUpc(String upc, String zipCode) {
        try {
            log.info("Looking up Walmart product by UPC: {}, zipCode: {}", upc, zipCode);
            
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(WALMART_API_BASE_URL)
                    .queryParam("upc", upc)
                    .queryParam("format", "json");
            
            if (zipCode != null && !zipCode.trim().isEmpty()) {
                uriBuilder.queryParam("zipCode", zipCode);
            }
            
            String url = uriBuilder.toUriString();
            log.info("Walmart API URL: {}", url);
            
            HttpHeaders headers = createWalmartHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            
            log.info("Walmart API response status: {}", response.getStatusCode());
            return response.getBody();
            
        } catch (Exception e) {
            log.error("Error calling Walmart API for UPC {}: {}", upc, e.getMessage(), e);
            throw new RuntimeException("Failed to lookup Walmart product: " + e.getMessage(), e);
        }
    }

    /**
     * Lookup product by GTIN
     * @param gtin Global Trade Item Number
     * @param zipCode Optional zip code for location-specific pricing
     * @return JSON response from Walmart API
     */
    public String lookupByGtin(String gtin, String zipCode) {
        try {
            log.info("Looking up Walmart product by GTIN: {}, zipCode: {}", gtin, zipCode);
            
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(WALMART_API_BASE_URL)
                    .queryParam("gtin", gtin)
                    .queryParam("format", "json");
            
            if (zipCode != null && !zipCode.trim().isEmpty()) {
                uriBuilder.queryParam("zipCode", zipCode);
            }
            
            String url = uriBuilder.toUriString();
            log.info("Walmart API URL: {}", url);
            
            HttpHeaders headers = createWalmartHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            
            log.info("Walmart API response status: {}", response.getStatusCode());
            return response.getBody();
            
        } catch (Exception e) {
            log.error("Error calling Walmart API for GTIN {}: {}", gtin, e.getMessage(), e);
            throw new RuntimeException("Failed to lookup Walmart product: " + e.getMessage(), e);
        }
    }

    /**
     * Lookup multiple products by item IDs (up to 20 items)
     * @param itemIds Comma-separated list of item IDs
     * @param zipCode Optional zip code for location-specific pricing
     * @return JSON response from Walmart API
     */
    public String lookupByItemIds(String itemIds, String zipCode) {
        try {
            log.info("Looking up Walmart products by itemIds: {}, zipCode: {}", itemIds, zipCode);
            
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(WALMART_API_BASE_URL)
                    .queryParam("ids", itemIds)
                    .queryParam("format", "json");
            
            if (zipCode != null && !zipCode.trim().isEmpty()) {
                uriBuilder.queryParam("zipCode", zipCode);
            }
            
            String url = uriBuilder.toUriString();
            log.info("Walmart API URL: {}", url);
            
            HttpHeaders headers = createWalmartHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            
            log.info("Walmart API response status: {}", response.getStatusCode());
            return response.getBody();
            
        } catch (Exception e) {
            log.error("Error calling Walmart API for itemIds {}: {}", itemIds, e.getMessage(), e);
            throw new RuntimeException("Failed to lookup Walmart products: " + e.getMessage(), e);
        }
    }

    /**
     * Creates HTTP headers required for Walmart API authentication
     * @return HttpHeaders with all required Walmart API headers
     * @throws Exception if signature generation fails
     */
    private HttpHeaders createWalmartHeaders() throws Exception {
        Map<String, String> walmartHeaders = signatureGenerator.generateWalmartApiHeaders();
        
        HttpHeaders headers = new HttpHeaders();
        headers.set("WM_CONSUMER.ID", walmartHeaders.get("WM_CONSUMER.ID"));
        headers.set("WM_CONSUMER.INTIMESTAMP", walmartHeaders.get("WM_CONSUMER.INTIMESTAMP"));
        headers.set("WM_SEC.KEY_VERSION", walmartHeaders.get("WM_SEC.KEY_VERSION"));
        headers.set("WM_SEC.AUTH_SIGNATURE", walmartHeaders.get("WM_SEC.AUTH_SIGNATURE"));
        headers.set("Accept", "application/json");
        headers.set("Content-Type", "application/json");
        
        log.debug("Created Walmart API headers: {}", headers.toSingleValueMap());
        return headers;
    }
}
