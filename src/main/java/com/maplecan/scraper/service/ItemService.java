package com.maplecan.scraper.service;

import com.maplecan.scraper.model.AddNewProductRequest;
import com.maplecan.scraper.model.Item;
import com.maplecan.scraper.model.ScrapedData;
import com.maplecan.scraper.repository.ItemRepository;
import com.maplecan.scraper.repository.ScrapedDataRepository;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

@Service
public class ItemService {

    private final ItemRepository repository;

    public ItemService(ItemRepository repository, ScraperFactory scraperFactory) {
        this.repository = repository;
    }

    public Optional<Item> getByUrl(String url) {
        return repository.findByUrl(url);
    }

    public boolean existsByUrl(String url) {
        return repository.existsByUrl(url);
    }

    public boolean existsByItemNumber(String itemNumber) {
        return repository.existsByItemNumber(itemNumber);
    }

    public void save(Item data) {
        repository.save(data);
    }

    public boolean shouldScanItem(Item item, LocalDateTime now) {
        // Always scan if item was never scanned before
        if (item.getLastScanTime() == null) return true;

        if (item.getCreatedTimestamp() == null) return true;

        // STOP: If item was created more than 60 days ago, skip scanning
        if (item.getCreatedTimestamp().isBefore(now.minusDays(60))) {
            return false;
        }

        // If item was created within the last 2 days, scan aggressively
        if (item.getCreatedTimestamp().isAfter(now.minusDays(2))) {
            return true;
        }

        // If price changed recently, slow down scanning
        if (item.getLastPriceChangeTimestamp() != null) {
            long daysSincePriceChange = ChronoUnit.DAYS.between(item.getLastPriceChangeTimestamp(), now);
            if (daysSincePriceChange < 3) {
                return false;
            }
        }

        // Always scan if last scan was over 7 days ago
        long daysSinceLastScan = ChronoUnit.DAYS.between(item.getLastScanTime(), now);
        return daysSinceLastScan >= 7;
    }
}
