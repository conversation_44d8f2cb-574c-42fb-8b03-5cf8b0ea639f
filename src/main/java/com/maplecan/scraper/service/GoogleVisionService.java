package com.maplecan.scraper.service;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.vision.v1.*;
import com.maplecan.scraper.model.ItemDetail;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class GoogleVisionService {

    @Value("${google.cloud.vision.credentials}")
    private String base64Credentials;

    /**
     * Process receipt based on file type (PDF or image).
     */
    public Map<String, Object> processReceipt(String fileUrl) throws IOException {
        List<String> extractedText = extractTextFromFile(fileUrl);
        Map<String, Object> process = processReceipt2(extractedText.getFirst());
        return process;
//        String storeAddress = extractStoreAddress(extractedText);
//        String province = extractProvince(extractedText);
//        Map<String, ItemDetail> items = extractItemDetails(extractedText);
//        processDiscounts(items, extractedText);
//
//        Map<String, Object> result = new HashMap<>();
//        result.put("storeAddress", storeAddress);
//        result.put("province", province);
//        result.put("items", items);
//        return result;
    }

    public Map<String, Object> processReceipt2(String extractedText) {
        Map<String, Object> receiptData = new HashMap<>();

        // Step 1: Extract Member Number
        String memberNumber = extractMemberNumber(extractedText);
        extractedText = extractedText.replace(memberNumber, "").trim();
        receiptData.put("memberNumber", memberNumber);

        // Step 2: Extract Store Address
        String storeAddress = extractStoreAddress(extractedText);
        extractedText = extractedText.replace(storeAddress, "").trim();
        receiptData.put("storeAddress", storeAddress);

        String province = extractProvince(storeAddress);
        extractedText = extractedText.replace(province, "").trim();
        receiptData.put("province", province);

        // Step 3: Extract Barcode Number
        String barcodeNumber = extractBarcodeNumber(extractedText);
        extractedText = extractedText.replace(barcodeNumber, "").trim();
        receiptData.put("barcodeNumber", barcodeNumber);

        Map<String, ItemDetail> itemDetailMap = extractItemDetails2(extractedText);
        receiptData.put("items", itemDetailMap);

        return receiptData;
    }

    /**
     * Extracts the member number from the receipt text.
     */
    private String extractMemberNumber(String text) {
        Pattern memberPattern = Pattern.compile("(?i)Member\\s*(\\d+)");
        Matcher matcher = memberPattern.matcher(text);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "Member number not found";
    }

    private String extractStoreAddress(String text) {
        Pattern storePattern = Pattern.compile(
                "(?i)COSTCO\\s+WHOLESALE\\s+([A-Z0-9#\\s]+)\\n([A-Z0-9\\s]+)\\n([A-Z]+,\\s[A-Z]{2}\\s[A-Z0-9]{3}\\s*[A-Z0-9]{3})"
        );
        Matcher matcher = storePattern.matcher(text);
        if (matcher.find()) {
            return matcher.group(1) + "\n" + matcher.group(2) + "\n" + matcher.group(3);
        }
        return "Store address not found";
    }

    private static final String PROVINCE_REGEX = "\\b(AB|BC|MB|NB|NL|NS|ON|PE|QC|SK|NT|NU|YT)\\b";

    public String extractProvince(String storeAddress) {
        Pattern pattern = Pattern.compile(PROVINCE_REGEX);
        Matcher matcher = pattern.matcher(storeAddress);

        if (matcher.find()) {
            return matcher.group(1); // Extracted province abbreviation
        }
        return "Unknown";  // Default if no province is found
    }

    /**
     * Extracts the barcode number from the receipt text.
     */
    private String extractBarcodeNumber(String text) {
        Pattern barcodePattern = Pattern.compile("\\b\\d{15,}\\b");
        Matcher matcher = barcodePattern.matcher(text);
        if (matcher.find()) {
            return matcher.group(0);
        }
        return "Barcode number not found";
    }

    private List<String> extractTextFromFile(String fileUrl) throws IOException {
        // Remove query parameters from URL for type detection
        String cleanUrl = fileUrl.split("\\?")[0];

        if (cleanUrl.toLowerCase().endsWith(".pdf")) {
            return processPdf(fileUrl);
        } else if (cleanUrl.toLowerCase().matches(".*\\.(jpg|jpeg|png|gif|bmp)$")) {
            return processImage(fileUrl);
        } else {
            throw new IllegalArgumentException("Unsupported file format. Please upload a valid image or PDF.");
        }
    }

    private List<String> processPdf(String fileUrl) throws IOException {
        List<String> extractedText = new ArrayList<>();
        try (ImageAnnotatorClient vision = getVisionClient()) {
            ImageSource imgSource = ImageSource.newBuilder().setImageUri(fileUrl).build();
            Image image = Image.newBuilder().setSource(imgSource).build();

            AnnotateImageRequest request = AnnotateImageRequest.newBuilder()
                    .setImage(image)
                    .addFeatures(Feature.newBuilder().setType(Feature.Type.DOCUMENT_TEXT_DETECTION))
                    .build();

            BatchAnnotateImagesResponse response = vision.batchAnnotateImages(List.of(request));
            for (AnnotateImageResponse res : response.getResponsesList()) {
                if (res.hasError()) {
                    throw new IOException("Error in processing PDF: " + res.getError().getMessage());
                }
                extractedText.add(res.getFullTextAnnotation().getText());
            }
        }
        return extractedText;
    }

    private List<String> processImage(String fileUrl) throws IOException {
        List<String> extractedText = new ArrayList<>();
        try (ImageAnnotatorClient vision = getVisionClient()) {
            ImageSource imgSource = ImageSource.newBuilder().setImageUri(fileUrl).build();
            Image image = Image.newBuilder().setSource(imgSource).build();

            AnnotateImageRequest request = AnnotateImageRequest.newBuilder()
                    .setImage(image)
                    .addFeatures(Feature.newBuilder().setType(Feature.Type.TEXT_DETECTION))
                    .build();

            BatchAnnotateImagesResponse response = vision.batchAnnotateImages(List.of(request));
            for (AnnotateImageResponse res : response.getResponsesList()) {
                if (res.hasError()) {
                    throw new IOException("Error in processing image: " + res.getError().getMessage());
                }
                extractedText.add(res.getFullTextAnnotation().getText());
            }
        }
        return extractedText;
    }

    private ImageAnnotatorClient getVisionClient() throws IOException {
        byte[] decodedCredentials = Base64.getDecoder().decode(base64Credentials);
        GoogleCredentials credentials = GoogleCredentials.fromStream(new ByteArrayInputStream(decodedCredentials));
        ImageAnnotatorSettings settings = ImageAnnotatorSettings.newBuilder()
                .setCredentialsProvider(() -> credentials)
                .build();
        return ImageAnnotatorClient.create(settings);
    }

    public Map<String, ItemDetail> extractItemDetails2(String extractedText) {
        Map<String, ItemDetail> items = new LinkedHashMap<>();
        String[] lines = extractedText.split("\n");

        String currentItemNumber = null;
        StringBuilder currentItemName = new StringBuilder();
        Double currentPrice = null;
        boolean awaitingPrice = false;

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();

            // Ignore lines containing deposits and eco fees
            if (line.contains("DEPOSIT/") || line.contains("ECO FEE ADS/")) {
                continue;  // Skip this line and move to the next one
            }

            // Check for discount pattern (e.g., "1899835 TPD/70692 2.00-")
            // Check for discount pattern (supports multi-line and single-line formats)
            if (line.contains("TPD/")) {
                String[] parts = line.split("\\s+");

                // Handle different line break scenarios for discount amount
                String discountAmountStr = null;
                if (parts.length == 2 && (i + 1) < lines.length && lines[i + 1].trim().matches("-?\\d+\\.\\d{2}-?")) {
                    // Case: TPD on one line, discount on the next line
                    discountAmountStr = lines[i + 1].trim().replace("-", "").trim();
                    i++; // Skip the next line as it contains the discount amount
                } else if (parts.length == 3) {
                    // Case: All values on a single line (e.g. "1909910 TPD/MCCAFE 9.00-")
                    discountAmountStr = parts[2].replace("-", "").trim();
                }

                // Process the discount
                double discountAmount;
                if (discountAmountStr != null) {
                    try {
                        discountAmount = Double.parseDouble(discountAmountStr);
                    } catch (Exception e) {
                        discountAmount = 0;
                    }

                    // Apply discount to the last processed valid item
                    if (!items.isEmpty()) {
                        String lastItemKey = null;
                        for (String key : items.keySet()) {
                            lastItemKey = key;
                        }
                        if (lastItemKey != null) {
                            ItemDetail previousItem = items.get(lastItemKey);
                            previousItem.setDiscountApplied(true);
                            previousItem.setPriceAfterDiscount(previousItem.getPriceBeforeDiscount() - discountAmount);
                            previousItem.setDiscount(discountAmount);
                        }
                    }
                }
                continue;
            }

            // Check for single-line pattern (Item Number + Name + Price)
            Matcher matcherPattern1 = Pattern.compile("^(\\d+)\\s+([A-Za-z0-9%\\s-]+)\\s+(\\d+\\.\\d{2})\\s+[A-Z]$").matcher(line);
            if (matcherPattern1.matches()) {
                currentItemNumber = matcherPattern1.group(1);
                currentItemName = new StringBuilder(matcherPattern1.group(2).trim());
                currentPrice = Double.parseDouble(matcherPattern1.group(3));

                items.put(currentItemNumber, new ItemDetail(currentItemNumber, currentItemName.toString(), false, currentPrice, currentPrice, 0.0));
                continue;
            }

            // Check if line contains only the item number (Multiline case)
            Matcher matcherPattern2 = Pattern.compile("^(\\d+)$").matcher(line);
            if (matcherPattern2.matches()) {
                currentItemNumber = matcherPattern2.group(1);
                awaitingPrice = true;
                currentItemName = new StringBuilder();
                continue;
            } else if (awaitingPrice) {
                // When awaiting price, check if it's the price line
                Matcher priceMatcher = Pattern.compile("(\\d+\\.\\d{2})\\s+[A-Z]").matcher(line);
                if (priceMatcher.find()) {
                    currentPrice = Double.parseDouble(priceMatcher.group(1));
                    items.put(currentItemNumber, new ItemDetail(currentItemNumber, currentItemName.toString().trim(), false, currentPrice, currentPrice, 0.0));
                    awaitingPrice = false;
                } else {
                    Matcher priceMatcher2 = Pattern.compile("^(\\d+\\.\\d{2})$").matcher(line); // Ensure price-only line is matched
                    if (priceMatcher2.find()) {
                        currentPrice = Double.parseDouble(priceMatcher2.group(1));
                        items.put(currentItemNumber, new ItemDetail(currentItemNumber, currentItemName.toString().trim(), false, currentPrice, currentPrice, 0.0));
                        awaitingPrice = false;
                    } else {
                        currentItemName.append(line).append(" ");
                    }

                }
                continue;
            }

            // Check for split-line item number and name on one line, price on next
            Matcher matcherPattern3 = Pattern.compile("^(\\d+)\\s+(.+)$").matcher(line);

//            Matcher matcherPattern3 = Pattern.compile("^(\\d+)\\s+([A-Za-z0-9%\\s-]+)$").matcher(line);
            if (matcherPattern3.matches()) {
                currentItemNumber = matcherPattern3.group(1);
                currentItemName = new StringBuilder(matcherPattern3.group(2).trim());
                awaitingPrice = true;
                continue;
            }

            // Handle price-only line when previous item name was split across two lines
            Matcher priceMatcher = Pattern.compile("(\\d+\\.\\d{2})\\s+[A-Z]").matcher(line);
            if (priceMatcher.find() && currentItemNumber != null) {
                currentPrice = Double.parseDouble(priceMatcher.group(1));
                items.put(currentItemNumber, new ItemDetail(currentItemNumber, currentItemName.toString().trim(), false, currentPrice, currentPrice, 0.0));
                currentItemNumber = null;
                currentItemName = new StringBuilder();
            }
        }

        return items;
    }
}
