package com.maplecan.scraper.service;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class ScraperFactory {

    private final Map<String, Scraper> scraperMap = new HashMap<>();

    public ScraperFactory(CostcoCanadaScraper costcoCanadaScraper, CostcoUSAScraper costcoUSAScraper,
                         WayfairCanadaScraper wayfairCanadaScraper, WalmartScraper walmartScraper) {
        scraperMap.put("costco.ca", costcoCanadaScraper);
        scraperMap.put("costco.com", costcoUSAScraper);
        scraperMap.put("wayfair.ca", wayfairCanadaScraper);
        scraperMap.put("wayfair.com", wayfairCanadaScraper);
        scraperMap.put("walmart.com", walmartScraper);
    }

    public Scraper getScraper(String url) {
        if (url.contains("costco.ca")) {
            return scraperMap.get("costco.ca");
        } else if (url.contains("costco.com")) {
            return scraperMap.get("costco.com");
        } else if (url.contains("wayfair.ca")) {
            return scraperMap.get("wayfair.ca");
        } else if (url.contains("wayfair.com")) {
            return scraperMap.get("wayfair.com");
        } else if (url.contains("walmart.com")) {
            return scraperMap.get("walmart.com");
        }
        throw new IllegalArgumentException("No scraper available for the given URL");
    }

    public Scraper getScraperByStoreAndCountryCode(String countryCode) {
        if (countryCode.equals("COSTCOUSA")) {
            return scraperMap.get("costco.com");
        } else if (countryCode.equals("COSTCOCANADA")) {
            return scraperMap.get("costco.ca");
        } else if (countryCode.equals("WAYFAIRCANADA")) {
            return scraperMap.get("wayfair.ca");
        } else if (countryCode.equals("WAYFAIRUSA")) {
            return scraperMap.get("wayfair.com");
        } else if (countryCode.equals("WALMARTUSA")) {
            return scraperMap.get("walmart.com");
        }
        throw new IllegalArgumentException("No scraper available for the given URL");
    }
}
