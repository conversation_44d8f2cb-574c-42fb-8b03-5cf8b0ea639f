package com.maplecan.scraper.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;


import java.io.*;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

@Service
public class SupabaseStorageService {

    @Value("${supabase.url}")
    private String supabaseUrl;

    @Value("${supabase.service.key:}")
    private String supabaseServiceKey;

    @Value("${supabase.anon.key}")
    private String supabaseAnonKey;

    @Value("${supabase.project.ref}")
    private String projectRef;

    @Value("${s3.region}")
    private String region;

    @Value("${s3.bucket.name}")
    private String bucketName;

    /**
     * Upload a file to Supabase storage
     * @param fileBytes The file content as byte array
     * @param fileName The name of the file
     * @param contentType The MIME type of the file
     * @return The public URL of the uploaded file
     * @throws IOException If upload fails
     */
    public String uploadFile(byte[] fileBytes, String fileName, String contentType) throws IOException {
        // Extract base URL from auth URL
        String baseUrl = supabaseUrl.replace("/auth/v1", "");
        String uploadUrl = baseUrl + "/storage/v1/object/" + bucketName + "/" + fileName;

        HttpURLConnection connection = (HttpURLConnection) new URL(uploadUrl).openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Authorization", "Bearer " + supabaseServiceKey);
        connection.setRequestProperty("Content-Type", contentType);
        connection.setDoOutput(true);

        // Write file data
        try (OutputStream os = connection.getOutputStream()) {
            os.write(fileBytes);
        }

        int responseCode = connection.getResponseCode();
        if (responseCode != 200 && responseCode != 201) {
            // Read error response
            try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getErrorStream()))) {
                StringBuilder errorResponse = new StringBuilder();
                String line;
                while ((line = br.readLine()) != null) {
                    errorResponse.append(line);
                }
                throw new IOException("Failed to upload file to Supabase: " + responseCode + " - " + errorResponse.toString());
            }
        }

        // Return the public URL
        return baseUrl + "/storage/v1/object/public/" + bucketName + "/" + fileName;
    }

    /**
     * Upload a screenshot file with a generated filename using direct HTTP API with JWT token
     * @param fileBytes The screenshot content as byte array
     * @param userId The user ID to organize files by user
     * @param url The original URL being captured
     * @param jwtToken The user's JWT token for authentication
     * @return The public URL of the uploaded screenshot
     * @throws RuntimeException If upload fails
     */
    public String uploadScreenshot(byte[] fileBytes, String userId, String url, String jwtToken) {
        String fileName = generateScreenshotFileName(userId, url, null);
        String baseUrl = supabaseUrl.replace("/auth/v1", "");
        String uploadUrl = baseUrl + "/storage/v1/object/" + bucketName + "/" + fileName;

        try {
            URL url_obj = new URL(uploadUrl);
            HttpURLConnection connection = (HttpURLConnection) url_obj.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Authorization", "Bearer " + jwtToken);
            connection.setRequestProperty("Content-Type", "image/jpeg");
            connection.setDoOutput(true);

            // Write the file data
            try (OutputStream os = connection.getOutputStream()) {
                os.write(fileBytes);
                os.flush();
            }

            int responseCode = connection.getResponseCode();
            if (responseCode == 200 || responseCode == 201) {
                // Return the public URL
                return baseUrl + "/storage/v1/object/public/" + bucketName + "/" + fileName;
            } else {
                // Read error response
                StringBuilder errorResponse = new StringBuilder();
                try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getErrorStream()))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        errorResponse.append(line);
                    }
                }
                throw new IOException("Failed to upload screenshot to Supabase: " + responseCode + " - " + errorResponse.toString());
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to upload screenshot to Supabase storage: " + e.getMessage(), e);
        }
    }

    /**
     * Upload a screenshot file with supabaseId for organized daily scraping
     * @param fileBytes The screenshot content as byte array
     * @param userId The user ID to organize files by user
     * @param supabaseId The UUID for organizing Supabase storage
     * @param url The original URL being captured
     * @param jwtToken The user's JWT token for authentication
     * @return The public URL of the uploaded screenshot
     * @throws RuntimeException If upload fails
     */
    public String uploadScreenshotWithDataId(byte[] fileBytes, String userId, String supabaseId, String url, String jwtToken) {
        String fileName = generateScreenshotFileNameWithSupabaseId(userId, url, supabaseId);
        String baseUrl = supabaseUrl.replace("/auth/v1", "");
        String uploadUrl = baseUrl + "/storage/v1/object/" + bucketName + "/" + fileName;

        try {
            URL url_obj = new URL(uploadUrl);
            HttpURLConnection connection = (HttpURLConnection) url_obj.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Authorization", "Bearer " + jwtToken);
            connection.setRequestProperty("Content-Type", "image/jpeg");
            connection.setDoOutput(true);

            // Write the file data
            try (OutputStream os = connection.getOutputStream()) {
                os.write(fileBytes);
                os.flush();
            }

            int responseCode = connection.getResponseCode();
            if (responseCode == 200 || responseCode == 201) {
                // Return the public URL
                return baseUrl + "/storage/v1/object/public/" + bucketName + "/" + fileName;
            } else {
                // Read error response
                StringBuilder errorResponse = new StringBuilder();
                try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getErrorStream()))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        errorResponse.append(line);
                    }
                }
                throw new IOException("Failed to upload screenshot to Supabase: " + responseCode + " - " + errorResponse.toString());
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to upload screenshot to Supabase storage: " + e.getMessage(), e);
        }
    }

    /**
     * Generate a unique filename for screenshots organized by user ID
     * @param userId The user ID to organize files by user
     * @param url The original URL being captured
     * @param screenshotDataId Optional screenshot data ID for organized structure
     * @return A unique filename with user ID, URL hash, and timestamp
     */
    private String generateScreenshotFileName(String userId, String url, String screenshotDataId) {
        try {
            // Create hash of URL for filename
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(url.getBytes());
            StringBuilder hashString = new StringBuilder();
            for (byte b : hashBytes) {
                hashString.append(String.format("%02x", b));
            }

            // Generate timestamp and date
            Instant now = Instant.now();
            long timestamp = now.toEpochMilli();
            String date = now.atZone(java.time.ZoneId.systemDefault()).toLocalDate().toString(); // YYYY-MM-DD format

            if (screenshotDataId != null && !screenshotDataId.isEmpty()) {
                // New organized structure: userId/screenshotDataId/urlHash-YYYY-MM-DD-timestamp.jpg
                return userId + "/" + screenshotDataId + "/" + hashString.toString() + "-" + date + "-" + timestamp + ".jpg";
            } else {
                // Legacy structure for backward compatibility: userId/urlHash_timestamp.jpg
                return userId + "/" + hashString.toString() + "_" + timestamp + ".jpg";
            }
        } catch (NoSuchAlgorithmException e) {
            // Fallback to simple timestamp if MD5 fails
            long timestamp = Instant.now().toEpochMilli();
            String date = Instant.now().atZone(java.time.ZoneId.systemDefault()).toLocalDate().toString();

            if (screenshotDataId != null && !screenshotDataId.isEmpty()) {
                return userId + "/" + screenshotDataId + "/screenshot-" + date + "-" + timestamp + ".jpg";
            } else {
                return userId + "/screenshot_" + timestamp + ".jpg";
            }
        }
    }

    /**
     * Delete a file from Supabase storage
     * @param fileName The name of the file to delete
     * @throws IOException If deletion fails
     */
    public void deleteFile(String fileName) throws IOException {
        String baseUrl = supabaseUrl.replace("/auth/v1", "");
        String deleteUrl = baseUrl + "/storage/v1/object/" + bucketName + "/" + fileName;

        HttpURLConnection connection = (HttpURLConnection) new URL(deleteUrl).openConnection();
        connection.setRequestMethod("DELETE");
        connection.setRequestProperty("Authorization", "Bearer " + supabaseServiceKey);

        int responseCode = connection.getResponseCode();
        if (responseCode != 200 && responseCode != 204) {
            throw new IOException("Failed to delete file from Supabase: " + responseCode);
        }
    }

    /**
     * Generate organized filename with supabaseId for new structure
     * @param userId The user ID
     * @param url The URL being captured
     * @param supabaseId The UUID for organizing Supabase storage
     * @return Organized filename: userId/supabaseId/screenshots/urlHash-YYYY-MM-DD-timestamp.jpg
     */
    private String generateScreenshotFileNameWithSupabaseId(String userId, String url, String supabaseId) {
        try {
            // Create hash of URL for filename
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(url.getBytes());
            StringBuilder hashString = new StringBuilder();
            for (byte b : hashBytes) {
                hashString.append(String.format("%02x", b));
            }

            // Generate timestamp
            LocalDateTime now = LocalDateTime.now();
            String date = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String timestamp = String.valueOf(now.toEpochSecond(ZoneOffset.UTC));

            // New organized structure: userId/supabaseId/screenshots/urlHash-YYYY-MM-DD-timestamp.jpg
            return userId + "/" + supabaseId + "/screenshots/" + hashString.toString() + "-" + date + "-" + timestamp + ".jpg";
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 algorithm not available", e);
        }
    }

    /**
     * Generate a filename for daily automated screenshots
     * @param userId The user ID
     * @param screenshotDataId The screenshot data ID
     * @param url The URL being tracked
     * @return Organized filename for daily scraping: userId/screenshotDataId/urlHash-YYYY-MM-DD-timestamp.jpg
     */
    public String generateDailyScreenshotFileName(String userId, String screenshotDataId, String url) {
        return generateScreenshotFileName(userId, url, screenshotDataId);
    }

    /**
     * Extract the file path from a Supabase storage URL
     * @param supabaseUrl The full Supabase storage URL
     * @return The file path within the bucket
     */
    public String extractFilePathFromUrl(String supabaseUrl) {
        // URL format: https://tovaxwkyjnrfssqzpmcl.supabase.co/storage/v1/object/public/items/userId/screenshotDataId/filename.jpg
        String publicPrefix = "/storage/v1/object/public/" + bucketName + "/";
        int startIndex = supabaseUrl.indexOf(publicPrefix);
        if (startIndex != -1) {
            return supabaseUrl.substring(startIndex + publicPrefix.length());
        }
        return null;
    }
}
