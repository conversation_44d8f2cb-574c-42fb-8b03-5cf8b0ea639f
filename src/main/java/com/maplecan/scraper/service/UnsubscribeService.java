package com.maplecan.scraper.service;

import com.maplecan.scraper.model.UserMetadata;
import com.maplecan.scraper.repository.UserMetadataRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Optional;

@Service
@Slf4j
public class UnsubscribeService {
    
    @Autowired
    private UserMetadataRepository userMetadataRepository;
    
    @Value("${unsubscribe.secret:default-secret-key-change-in-production}")
    private String unsubscribeSecret;
    
    private static final String HMAC_ALGORITHM = "HmacSHA256";
    private static final long TOKEN_VALIDITY_HOURS = 24; // Token valid for 24 hours
    
    /**
     * Generate a secure unsubscribe token for an email address
     */
    public String generateUnsubscribeToken(String email) {
        try {
            long timestamp = System.currentTimeMillis();
            String payload = email + ":" + timestamp;
            
            Mac mac = Mac.getInstance(HMAC_ALGORITHM);
            SecretKeySpec secretKeySpec = new SecretKeySpec(unsubscribeSecret.getBytes(StandardCharsets.UTF_8), HMAC_ALGORITHM);
            mac.init(secretKeySpec);
            
            byte[] signature = mac.doFinal(payload.getBytes(StandardCharsets.UTF_8));
            String encodedSignature = Base64.getUrlEncoder().withoutPadding().encodeToString(signature);
            
            String token = Base64.getUrlEncoder().withoutPadding().encodeToString(payload.getBytes(StandardCharsets.UTF_8)) + "." + encodedSignature;
            
            log.debug("Generated unsubscribe token for email: {}", email);
            return token;
            
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("Failed to generate unsubscribe token for email: {}", email, e);
            throw new RuntimeException("Failed to generate unsubscribe token", e);
        }
    }
    
    /**
     * Validate and extract email from unsubscribe token
     */
    public Optional<String> validateAndExtractEmail(String token) {
        try {
            String[] parts = token.split("\\.");
            if (parts.length != 2) {
                log.warn("Invalid token format");
                return Optional.empty();
            }
            
            String payload = new String(Base64.getUrlDecoder().decode(parts[0]), StandardCharsets.UTF_8);
            String providedSignature = parts[1];
            
            String[] payloadParts = payload.split(":");
            if (payloadParts.length != 2) {
                log.warn("Invalid payload format");
                return Optional.empty();
            }
            
            String email = payloadParts[0];
            long timestamp = Long.parseLong(payloadParts[1]);
            
            // Check if token is expired
            long currentTime = System.currentTimeMillis();
            if (currentTime - timestamp > TOKEN_VALIDITY_HOURS * 60 * 60 * 1000) {
                log.warn("Token expired for email: {}", email);
                return Optional.empty();
            }
            
            // Verify signature
            Mac mac = Mac.getInstance(HMAC_ALGORITHM);
            SecretKeySpec secretKeySpec = new SecretKeySpec(unsubscribeSecret.getBytes(StandardCharsets.UTF_8), HMAC_ALGORITHM);
            mac.init(secretKeySpec);
            
            byte[] expectedSignature = mac.doFinal(payload.getBytes(StandardCharsets.UTF_8));
            String expectedSignatureString = Base64.getUrlEncoder().withoutPadding().encodeToString(expectedSignature);
            
            if (!expectedSignatureString.equals(providedSignature)) {
                log.warn("Invalid signature for email: {}", email);
                return Optional.empty();
            }
            
            log.debug("Successfully validated token for email: {}", email);
            return Optional.of(email);
            
        } catch (Exception e) {
            log.error("Failed to validate token", e);
            return Optional.empty();
        }
    }
    
    /**
     * Unsubscribe a user by email
     */
    public boolean unsubscribeUser(String email, String reason) {
        try {
            UserMetadata userMetadata = userMetadataRepository.findByEmail(email)
                .orElse(UserMetadata.builder()
                    .email(email)
                    .emailSubscription(false)
                    .build());
            
            userMetadata.setEmailSubscription(false);
            userMetadata.setUnsubscribedAt(LocalDateTime.now());
            userMetadata.setUnsubscribeReason(reason);
            userMetadata.setUpdatedAt(LocalDateTime.now());
            
            userMetadataRepository.save(userMetadata);
            
            log.info("Successfully unsubscribed user: {}", email);
            return true;
            
        } catch (Exception e) {
            log.error("Failed to unsubscribe user: {}", email, e);
            return false;
        }
    }
    
    /**
     * Check if a user is subscribed to emails
     */
    public boolean isUserSubscribed(String email) {
        return userMetadataRepository.findByEmail(email)
            .map(UserMetadata::getEmailSubscription)
            .orElse(true); // Default to subscribed if no record exists
    }
    
    /**
     * Get or create user metadata
     */
    public UserMetadata getOrCreateUserMetadata(String email) {
        return userMetadataRepository.findByEmail(email)
            .orElse(UserMetadata.builder()
                .email(email)
                .emailSubscription(true)
                .build());
    }

    /**
     * Get user metadata by email
     */
    public Optional<UserMetadata> getUserMetadata(String email) {
        return userMetadataRepository.findByEmail(email);
    }

    /**
     * Update user subscription status
     */
    public boolean updateEmailSubscription(String email, boolean subscribed) {
        try {
            Optional<UserMetadata> userOpt = userMetadataRepository.findByEmail(email);
            UserMetadata user;

            if (userOpt.isPresent()) {
                user = userOpt.get();
                user.setEmailSubscription(subscribed);
                user.setUpdatedAt(LocalDateTime.now());

                // Clear unsubscribe details if re-subscribing
                if (subscribed) {
                    user.setUnsubscribedAt(null);
                    user.setUnsubscribeReason(null);
                }
            } else {
                // Create new user metadata if doesn't exist
                user = UserMetadata.builder()
                    .email(email)
                    .emailSubscription(subscribed)
                    .build();
            }

            userMetadataRepository.save(user);
            log.info("Updated email subscription for {}: {}", email, subscribed);
            return true;
        } catch (Exception e) {
            log.error("Failed to update email subscription for {}", email, e);
            return false;
        }
    }

    /**
     * Update user personal information
     */
    public boolean updateUserInfo(String email, String firstName, String lastName) {
        try {
            Optional<UserMetadata> userOpt = userMetadataRepository.findByEmail(email);
            UserMetadata user;

            if (userOpt.isPresent()) {
                user = userOpt.get();
            } else {
                // Create new user metadata if doesn't exist
                user = UserMetadata.builder()
                    .email(email)
                    .emailSubscription(true)
                    .build();
            }

            user.setFirstName(firstName);
            user.setLastName(lastName);
            user.setUpdatedAt(LocalDateTime.now());

            userMetadataRepository.save(user);
            log.info("Updated user info for {}: {} {}", email, firstName, lastName);
            return true;
        } catch (Exception e) {
            log.error("Failed to update user info for {}", email, e);
            return false;
        }
    }
}
