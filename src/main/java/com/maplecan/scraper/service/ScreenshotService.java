package com.maplecan.scraper.service;

import com.maplecan.scraper.model.ScreenshotData;
import com.maplecan.scraper.model.ScreenshotResponse;
import com.maplecan.scraper.repository.ScreenshotDataRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.maplecan.scraper.service.GenericScreenshotScraper;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for capturing screenshots using the screenshot factory pattern
 */
@Slf4j
@Service
public class ScreenshotService {

    private final ScreenshotFactory screenshotFactory;
    private final SupabaseStorageService supabaseStorageService;
    private final ScreenshotDataRepository screenshotDataRepository;
    private final BaselineImageService baselineImageService;

    public ScreenshotService(ScreenshotFactory screenshotFactory,
                           SupabaseStorageService supabaseStorageService,
                           ScreenshotDataRepository screenshotDataRepository,
                           BaselineImageService baselineImageService) {
        this.screenshotFactory = screenshotFactory;
        this.supabaseStorageService = supabaseStorageService;
        this.screenshotDataRepository = screenshotDataRepository;
        this.baselineImageService = baselineImageService;
    }

    /**
     * Captures a screenshot of the given URL and saves it to Supabase storage
     * @param url The URL to capture
     * @return ScreenshotResponse with screenshot details
     * @throws IOException if screenshot capture fails
     */
    public ScreenshotResponse captureScreenshot(String url) throws IOException {
        log.info("Capturing screenshot for URL: {}", url);

        // Get the appropriate screenshot scraper
        ScreenshotScraper screenshotScraper = screenshotFactory.getScreenshotScraper(url);

        // Capture screenshot using the selected scraper
        byte[] screenshotBytes = screenshotScraper.captureScreenshot(url);

        // Upload to Supabase storage (using service key for anonymous uploads)
        String screenshotUrl = supabaseStorageService.uploadFile(screenshotBytes, "anonymous/" + System.currentTimeMillis() + ".jpg", "image/jpeg");

        return ScreenshotResponse.builder()
                .screenshotUrl(screenshotUrl)
                .originalUrl(url)
                .capturedAt(LocalDateTime.now())
                .status("success")
                .message("Screenshot captured successfully")
                .build();
    }

    /**
     * Captures a screenshot and saves metadata to database
     * @param url The URL to capture
     * @param userId The user ID
     * @param userEmail The user email
     * @param supabaseId The UUID for organizing Supabase storage
     * @param jwtToken The user's JWT token for authentication
     * @return ScreenshotData with saved metadata including ID
     * @throws IOException if screenshot capture fails
     */
    public ScreenshotData captureScreenshotWithMetadata(String url, String userId, String userEmail, String supabaseId, String jwtToken) throws IOException {
        log.info("Capturing screenshot with metadata for URL: {} (user: {})", url, userId);

        // Get the appropriate screenshot scraper
        ScreenshotScraper screenshotScraper = screenshotFactory.getScreenshotScraper(url);

        // Capture screenshot using the selected scraper
        byte[] screenshotBytes = screenshotScraper.captureScreenshot(url);

        // First, save metadata to database to get the ID
        ScreenshotData screenshotData = ScreenshotData.builder()
                .url(url)
                .screenshotUrl("") // Temporary empty URL
                .supabaseId(supabaseId)
                .userId(userId)
                .userEmail(userEmail)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        ScreenshotData savedData = screenshotDataRepository.save(screenshotData);

        // Now upload to Supabase storage with organized structure using the supabaseId
        String screenshotUrl = supabaseStorageService.uploadScreenshotWithDataId(
                screenshotBytes, userId, supabaseId, url, jwtToken);

        // Update the screenshot URL in the database
        savedData.setScreenshotUrl(screenshotUrl);
        savedData.setUpdatedAt(LocalDateTime.now());

        ScreenshotData finalSavedData = screenshotDataRepository.save(savedData);

        // Automatically create baseline image of the entire screenshot
        try {
            String baselineImageUrl = baselineImageService.createFullImageBaseline(finalSavedData, jwtToken);
            finalSavedData.setBaselineImageUrl(baselineImageUrl);
            finalSavedData = screenshotDataRepository.save(finalSavedData);
            log.info("Successfully created full-image baseline for new screenshot ID: {}", finalSavedData.getId());
        } catch (Exception e) {
            log.warn("Failed to create full-image baseline for screenshot ID: {}", finalSavedData.getId(), e);
            // Continue without failing the screenshot creation
        }

        return finalSavedData;
    }

    /**
     * Captures a daily screenshot for automated comparison
     * @param screenshotData The existing screenshot data record
     * @param jwtToken The user's JWT token for authentication
     * @return The URL of the newly captured screenshot
     * @throws IOException if screenshot capture fails
     */
    public String captureDailyScreenshot(ScreenshotData screenshotData, String jwtToken) throws IOException {
        log.info("Capturing daily screenshot for tracking ID: {} (URL: {})",
                screenshotData.getId(), screenshotData.getUrl());

        // Get the appropriate screenshot scraper
        ScreenshotScraper screenshotScraper = screenshotFactory.getScreenshotScraper(screenshotData.getUrl());

        // Capture screenshot using the selected scraper
        byte[] screenshotBytes = screenshotScraper.captureScreenshot(screenshotData.getUrl());

        // Upload to organized structure: userId/screenshotDataId/urlHash-YYYY-MM-DD-timestamp.jpg
        return supabaseStorageService.uploadScreenshotWithDataId(
                screenshotBytes,
                screenshotData.getUserId(),
                screenshotData.getId(),
                screenshotData.getUrl(),
                jwtToken);
    }

    /**
     * Get all active screenshot tracking records for daily scraping
     * @return List of active (non-deleted) screenshot data records
     */
    public List<ScreenshotData> getActiveScreenshotTrackingRecords() {
        return screenshotDataRepository.findByDeleted(false);
    }

    /**
     * Capture screenshot and return raw bytes (for comparison jobs)
     * @param url The URL to capture
     * @return Screenshot bytes
     * @throws IOException if screenshot capture fails
     */
    public byte[] captureScreenshotBytes(String url) throws IOException {
        return captureScreenshotBytes(url, false); // Default to viewport screenshot
    }

    /**
     * Capture screenshot and return raw bytes with configurable screenshot type
     * @param url The URL to capture
     * @param fullPage Whether to capture full page (true) or viewport only (false)
     * @return Screenshot bytes
     * @throws IOException if screenshot capture fails
     */
    public byte[] captureScreenshotBytes(String url, boolean fullPage) throws IOException {
        log.info("Capturing screenshot bytes for URL: {} (fullPage: {})", url, fullPage);

        // Get the appropriate screenshot scraper
        ScreenshotScraper screenshotScraper = screenshotFactory.getScreenshotScraper(url);

        // Check if the scraper supports configurable screenshot types
        if (screenshotScraper instanceof GenericScreenshotScraper) {
            GenericScreenshotScraper genericScraper = (GenericScreenshotScraper) screenshotScraper;
            return genericScraper.captureScreenshot(url, fullPage);
        } else {
            // Fallback to default method for other scrapers
            return screenshotScraper.captureScreenshot(url);
        }
    }

}
