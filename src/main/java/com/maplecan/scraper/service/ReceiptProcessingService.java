package com.maplecan.scraper.service;

import com.maplecan.scraper.model.ItemDetail;
import com.maplecan.scraper.model.ReceiptData;
import com.maplecan.scraper.repository.ReceiptDataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Map;

@Service
public class ReceiptProcessingService {

    @Autowired
    private GoogleVisionService googleVisionService;

    @Autowired
    private ReceiptDataRepository receiptDataRepository;

    public void processAndSaveReceipt(String receiptId, String fileUrl) throws IOException {
        // Process receipt using Google Vision
        Map<String, Object> processedData = googleVisionService.processReceipt(fileUrl);

        // Create ReceiptData object
        ReceiptData receiptData = new ReceiptData();
        receiptData.setReceiptId(receiptId);
        receiptData.setBarcodeNumber((String) processedData.get("barcodeNumber"));
        receiptData.setMemberNumber((String) processedData.get("memberNumber"));
        receiptData.setStoreAddress((String) processedData.get("storeAddress"));
        receiptData.setProvince((String) processedData.get("province"));
        receiptData.setItems((Map<String, ItemDetail>) processedData.get("items"));
        receiptData.setProcessedTime(LocalDateTime.now());

        // Save to MongoDB
        receiptDataRepository.save(receiptData);
    }
}
