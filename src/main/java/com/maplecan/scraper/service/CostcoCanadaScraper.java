package com.maplecan.scraper.service;

import com.maplecan.scraper.model.ScrapedData;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class CostcoCanadaScraper implements Scraper {

    private final RestTemplate restTemplate;

    @Value("${scrapingbee.api.key}")
    private String apiKey;

    public CostcoCanadaScraper(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Override
    public ScrapedData fetch(String url, String province) {
        String apiUrl = "https://app.scrapingbee.com/api/v1/";
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(apiUrl)
                .queryParam("api_key", apiKey)
                .queryParam("wait_for", "span.value.canada-currency-size")
                .queryParam("render_js", true)
                .queryParam("premium_proxy", true)
                .queryParam("url", url)
                .queryParam("block_resources", false)
                .queryParam("country_code", "ca")
                .queryParam("stealth_proxy", true);


        try {
            String htmlContent = restTemplate.getForObject(uriBuilder.toUriString(), String.class);

            Double price = Double.parseDouble(extractPriceFromScript(htmlContent));
            Double discount = extractDiscount(htmlContent);
            Double ecoFee = extractEcoFeeForProvince(htmlContent, "CA" + province);
            Double actualPrice = price - discount + ecoFee;
            String imageUrl = extractImgUrl(htmlContent);
            String itemNumber = extractItemNumber(htmlContent);
            String itemNumberAppended = "COSTCOCANADA" + itemNumber;

            return ScrapedData.builder()
                    .url(url)
                    .imageUrl(imageUrl)
                    .price(actualPrice)
                    .province(province)
                    .itemNumber(itemNumberAppended)
                    .isAvailable(true)
                    .build();

        } catch (org.springframework.web.client.HttpClientErrorException.NotFound ex) {
            // Handle 404 - item not found
            return ScrapedData.builder()
                    .url(url)
                    .province(province)
                    .isAvailable(false)
                    .build();
        }
    }

    @Override
    public ScrapedData fetchByItemNumber(String itemNumber) {
        String apiUrl = "https://app.scrapingbee.com/api/v1/";
//        String url = "https://costco.ca/ProductDisplay?partNumber=" + itemNumber;
        String url = "https://costco.ca/.product." + itemNumber + ".html";
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(apiUrl)
                .queryParam("api_key", apiKey)
                .queryParam("wait_for", "span.value.canada-currency-size")
                .queryParam("render_js", true)
                .queryParam("premium_proxy", true)
                .queryParam("url", url)
                .queryParam("block_resources", false)
                .queryParam("country_code", "ca")
                .queryParam("stealth_proxy", true);

        String itemNumberAppended = "COSTCOCANADA" + itemNumber;
        try {

            String test = restTemplate.getForObject(uriBuilder.toUriString(), String.class);

            // Extract the price using regex
            Double price = Double.parseDouble(extractPriceFromScript(test));
            Double discount = extractDiscount(test);
            Double actualPrice = price - discount;
            String imageUrl = extractImgUrl(test);


            ScrapedData data = ScrapedData.builder()
                    .url(url)
                    .productName(extractProductName(test))
                    .imageUrl(imageUrl)
                    .price(actualPrice)
                    .province("ON")
                    .itemNumber(itemNumberAppended)
                    .build();

            return data;
        } catch (org.springframework.web.client.HttpClientErrorException.NotFound ex) {
            // Handle 404 - item not found
            return ScrapedData.builder()
                    .url(url)
                    .province("ON")
                    .itemNumber(itemNumberAppended)
                    .isAvailable(false)
                    .build();
        }
    }

    private String extractProductName(String htmlContent) {
        // Regex pattern to match the product title inside <span class="product-title-sticky">
        String regex = "<span[^>]*class=\"product-title-sticky\"[^>]*>(.*?)</span>";
        Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);
        Matcher matcher = pattern.matcher(htmlContent);

        if (matcher.find()) {
            return matcher.group(1).trim(); // Extracted product name
        }
        return "Product Name Not Found";
    }

    private String extractPriceFromScript(String htmlContent) {
        String regex = "priceTotal: initialize\\((\\d+\\.\\d+)\\)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(htmlContent);

        if (matcher.find()) {
            return matcher.group(1); // Extract the price
        }
        return null;
    }

    private Double extractDiscount(String htmlContent) {
        // Regex to match the specific div and p class containing the discount
        String regex = "<div[^>]*class=\"marketing-container form-group\"[^>]*>\\s*<p[^>]*class=\"merchandisingText\"[^>]*>\\s*\\$([\\d.,]+)\\s*OFF</p>";
        Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);
        Matcher matcher = pattern.matcher(htmlContent);

        if (matcher.find()) {
            String discount = matcher.group(1).replace(",", ""); // Remove commas for large numbers
            return Double.parseDouble(discount); // Parse and return the discount
        }
        return 0.0; // No discount found
    }

    private Double extractEcoFeeForProvince(String htmlContent, String province) {
        String regex = "\"region\"\\s*:\\s*\"" + province + "\"\\s*,\\s*\"desc\"\\s*:\\s*\"ECO FEE ADS\"\\s*,\\s*\"price\"\\s*:\\s*\"([\\d.]+)\"";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(htmlContent);

        if (matcher.find()) {
            return Double.parseDouble(matcher.group(1));
        }
        return 0.0; // No Eco Fee found for the province
    }

    private String extractImgUrl(String htmlContent) {
        // Regex to match the img_url field
        String regex = "\"img_url\"\\s*:\\s*\"(https?://[^\"]+)\"";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(htmlContent);

        if (matcher.find()) {
            return matcher.group(1); // Return the first matched URL
        }
        return null; // Return null if no match is found
    }

    private String extractItemNumber(String htmlContent) {
        // Regex to match the initialize function and extract the value inside
        String regex = "SKU:\\s*initialize\\('\\s*([\\w\\d]+)\\s*'\\)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(htmlContent);

        if (matcher.find()) {
            return matcher.group(1); // Return the captured item number
        }
        return null; // Return null if no match is found
    }
}
