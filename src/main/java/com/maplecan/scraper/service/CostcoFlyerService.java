package com.maplecan.scraper.service;

import com.maplecan.scraper.model.Flyers;
import com.maplecan.scraper.repository.FlyerRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Optional;

@Service
public class CostcoFlyerService {

    @Autowired
    private CostcoCanadaCouponScraper costcoCanadaCouponScraper;

    @Autowired
    private FlyerRepository flyerRepository;

    public Optional<Flyers> getLatestFlyer(String vendorName) {
        return flyerRepository.findTopByVendorOrderByCreatedTimestampDesc(vendorName);
    }

    public Flyers scrapeFlyer(String postalCode) throws IOException {
        return costcoCanadaCouponScraper.scrapeCoupons(postalCode);
    }

    public void screenshotFlyer(String postalCode) throws IOException {
        costcoCanadaCouponScraper.scrapeCouponsWithScreenshot(postalCode);
    }

    public Flyers scrapeAndSaveFlyer(String postalCode) throws IOException {
        Flyers flyer = costcoCanadaCouponScraper.scrapeCoupons(postalCode);
        return saveFlyer(flyer);
    }

    public Flyers saveFlyer(Flyers flyer) throws IOException {
        return flyerRepository.save(flyer);
    }
}
