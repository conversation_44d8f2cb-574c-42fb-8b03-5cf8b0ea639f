package com.maplecan.scraper.dto;

import com.maplecan.scraper.model.ImageComparisonResult;
import lombok.Builder;
import lombok.Data;

import java.awt.*;
import java.time.LocalDateTime;

@Data
@Builder
public class ImageComparisonResponse {
    
    /**
     * Overall similarity score (0.0 to 1.0, where 1.0 is identical)
     */
    private double overallSimilarity;
    
    /**
     * Pixel-by-pixel similarity score
     */
    private double pixelSimilarity;
    
    /**
     * Structural similarity score (simplified SSIM)
     */
    private double structuralSimilarity;
    
    /**
     * Histogram similarity score
     */
    private double histogramSimilarity;
    
    /**
     * Perceptual hash similarity score
     */
    private double perceptualHashSimilarity;
    
    /**
     * Whether the images have any detectable differences
     */
    private boolean hasDifferences;
    
    /**
     * Whether the images have significant differences (below 85% similarity)
     */
    private boolean significantDifferences;
    
    /**
     * Original dimensions of first image
     */
    private DimensionDto image1Dimensions;
    
    /**
     * Original dimensions of second image
     */
    private DimensionDto image2Dimensions;
    
    /**
     * Dimensions used for comparison (normalized size)
     */
    private DimensionDto comparisonDimensions;
    
    /**
     * Timestamp when comparison was performed
     */
    private LocalDateTime comparisonTimestamp;
    
    /**
     * Similarity percentage as a formatted string
     */
    private String similarityPercentage;
    
    /**
     * Human-readable assessment of the comparison
     */
    private String assessment;
    
    /**
     * Detailed breakdown of similarity metrics
     */
    private String detailedBreakdown;
    
    /**
     * URL to access the difference map image (if stored)
     */
    private String differenceMapUrl;
    
    /**
     * Convert from ImageComparisonResult to API response
     */
    public static ImageComparisonResponse fromResult(ImageComparisonResult result) {
        return ImageComparisonResponse.builder()
                .overallSimilarity(result.getOverallSimilarity())
                .pixelSimilarity(result.getPixelSimilarity())
                .structuralSimilarity(result.getStructuralSimilarity())
                .histogramSimilarity(result.getHistogramSimilarity())
                .perceptualHashSimilarity(result.getPerceptualHashSimilarity())
                .hasDifferences(result.isHasDifferences())
                .significantDifferences(result.isSignificantDifferences())
                .image1Dimensions(DimensionDto.fromDimension(result.getImage1Dimensions()))
                .image2Dimensions(DimensionDto.fromDimension(result.getImage2Dimensions()))
                .comparisonDimensions(DimensionDto.fromDimension(result.getComparisonDimensions()))
                .comparisonTimestamp(result.getComparisonTimestamp())
                .similarityPercentage(result.getSimilarityPercentage())
                .assessment(result.getAssessment())
                .detailedBreakdown(result.getDetailedBreakdown())
                .build();
    }
    
    @Data
    @Builder
    public static class DimensionDto {
        private int width;
        private int height;
        
        public static DimensionDto fromDimension(Dimension dimension) {
            return DimensionDto.builder()
                    .width(dimension.width)
                    .height(dimension.height)
                    .build();
        }
    }
}
