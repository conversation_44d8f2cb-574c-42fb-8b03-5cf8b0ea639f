package com.maplecan.scraper.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class CreatePostRequest {
    
    @NotBlank(message = "Title is required")
    @Size(min = 5, max = 200, message = "Title must be between 5 and 200 characters")
    private String title;
    
    @NotBlank(message = "Content is required")
    @Size(min = 10, max = 5000, message = "Content must be between 10 and 5000 characters")
    private String content;
    
    private String productUrl;
    private String productName;
    private String productImage;
    private Double originalPrice;
    private Double currentPrice;
    
    @NotBlank(message = "Store is required")
    private String store; // costco, walmart, wayfair
    
    @NotBlank(message = "Category is required")
    private String category; // hot-deals, price-drops, general
}
