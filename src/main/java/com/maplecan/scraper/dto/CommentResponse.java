package com.maplecan.scraper.dto;

import com.maplecan.scraper.model.CommunityComment;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CommentResponse {
    private String id;
    private String postId;
    private String authorId;
    private String authorUsername;
    private String content;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Integer upvotes;
    private Integer downvotes;
    private String parentCommentId;
    private Integer depth;
    
    // User interaction state
    private <PERSON><PERSON><PERSON> hasUpvoted;
    private Boolean hasDownvoted;
    
    // Nested replies
    private List<CommentResponse> replies;
    
    public static CommentResponse fromEntity(CommunityComment comment, String authorUsername, Boolean hasUpvoted, Boolean hasDownvoted) {
        CommentResponse response = new CommentResponse();
        response.setId(comment.getId());
        response.setPostId(comment.getPostId());
        response.setAuthorId(comment.getAuthorId());
        response.setAuthorUsername(authorUsername);
        response.setContent(comment.getContent());
        response.setCreatedAt(comment.getCreatedAt());
        response.setUpdatedAt(comment.getUpdatedAt());
        response.setUpvotes(comment.getUpvotes());
        response.setDownvotes(comment.getDownvotes());
        response.setParentCommentId(comment.getParentCommentId());
        response.setDepth(comment.getDepth());
        response.setHasUpvoted(hasUpvoted);
        response.setHasDownvoted(hasDownvoted);
        return response;
    }
}
