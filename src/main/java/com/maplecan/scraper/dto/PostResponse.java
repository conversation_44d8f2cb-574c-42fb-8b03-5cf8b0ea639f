package com.maplecan.scraper.dto;

import com.maplecan.scraper.model.CommunityPost;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class PostResponse {
    private String id;
    private String authorId;
    private String authorUsername;
    private String title;
    private String content;
    private String productUrl;
    private String productName;
    private String productImage;
    private Double originalPrice;
    private Double currentPrice;
    private String store;
    private String category;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Integer upvotes;
    private Integer downvotes;
    private Integer commentCount;
    private Integer viewCount;
    private Boolean isFeatured;
    private List<String> tags;
    
    // User interaction state
    private Boolean hasUpvoted;
    private Boolean hasDownvoted;
    
    public static PostResponse fromEntity(CommunityPost post, String authorUsername, Boolean hasUpvoted, Boolean hasDownvoted) {
        PostResponse response = new PostResponse();
        response.setId(post.getId());
        response.setAuthorId(post.getAuthorId());
        response.setAuthorUsername(authorUsername);
        response.setTitle(post.getTitle());
        response.setContent(post.getContent());
        response.setProductUrl(post.getProductUrl());
        response.setProductName(post.getProductName());
        response.setProductImage(post.getProductImage());
        response.setOriginalPrice(post.getOriginalPrice());
        response.setCurrentPrice(post.getCurrentPrice());
        response.setStore(post.getStore());
        response.setCategory(post.getCategory());
        response.setCreatedAt(post.getCreatedAt());
        response.setUpdatedAt(post.getUpdatedAt());
        response.setUpvotes(post.getUpvotes());
        response.setDownvotes(post.getDownvotes());
        response.setCommentCount(post.getCommentCount());
        response.setViewCount(post.getViewCount());
        response.setIsFeatured(post.getIsFeatured());
        response.setTags(post.getTags());
        response.setHasUpvoted(hasUpvoted);
        response.setHasDownvoted(hasDownvoted);
        return response;
    }
}
