package com.maplecan.scraper.model;

import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.Map;

@Document(collection = "item_history")
@Data
@Builder
public class ItemHistory {

    @Id
    private String id;

    @Indexed
    private String url;

    private String imageUrl;

    private Double price;

    @Indexed
    private LocalDateTime dateTime;

    private Map<String, Object> metadata;

}
