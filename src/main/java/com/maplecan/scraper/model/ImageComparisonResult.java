package com.maplecan.scraper.model;

import lombok.Builder;
import lombok.Data;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.time.LocalDateTime;

@Data
@Builder
public class ImageComparisonResult {
    
    /**
     * Overall similarity score (0.0 to 1.0, where 1.0 is identical)
     */
    private double overallSimilarity;
    
    /**
     * Pixel-by-pixel similarity score
     */
    private double pixelSimilarity;
    
    /**
     * Structural similarity score (simplified SSIM)
     */
    private double structuralSimilarity;
    
    /**
     * Histogram similarity score
     */
    private double histogramSimilarity;
    
    /**
     * Perceptual hash similarity score
     */
    private double perceptualHashSimilarity;
    
    /**
     * Whether the images have any detectable differences
     */
    private boolean hasDifferences;
    
    /**
     * Whether the images have significant differences (below 85% similarity)
     */
    private boolean significantDifferences;
    
    /**
     * Visual difference map highlighting changed areas
     */
    private BufferedImage differenceMap;
    
    /**
     * Original dimensions of first image
     */
    private Dimension image1Dimensions;
    
    /**
     * Original dimensions of second image
     */
    private Dimension image2Dimensions;
    
    /**
     * Dimensions used for comparison (normalized size)
     */
    private Dimension comparisonDimensions;
    
    /**
     * Timestamp when comparison was performed
     */
    @Builder.Default
    private LocalDateTime comparisonTimestamp = LocalDateTime.now();
    
    /**
     * Get similarity percentage as a formatted string
     */
    public String getSimilarityPercentage() {
        return String.format("%.2f%%", overallSimilarity * 100);
    }
    
    /**
     * Get a human-readable assessment of the comparison
     */
    public String getAssessment() {
        if (overallSimilarity >= 0.98) {
            return "Images are virtually identical";
        } else if (overallSimilarity >= 0.95) {
            return "Images are very similar with minor differences";
        } else if (overallSimilarity >= 0.85) {
            return "Images are similar with noticeable differences";
        } else if (overallSimilarity >= 0.70) {
            return "Images have significant differences";
        } else if (overallSimilarity >= 0.50) {
            return "Images are quite different";
        } else {
            return "Images are very different";
        }
    }
    
    /**
     * Get detailed breakdown of similarity metrics
     */
    public String getDetailedBreakdown() {
        return String.format(
            "Overall: %.2f%% | Pixel: %.2f%% | Structural: %.2f%% | Histogram: %.2f%% | Perceptual: %.2f%%",
            overallSimilarity * 100,
            pixelSimilarity * 100,
            structuralSimilarity * 100,
            histogramSimilarity * 100,
            perceptualHashSimilarity * 100
        );
    }
    
    /**
     * Check if images are considered identical (above 98% similarity)
     */
    public boolean areIdentical() {
        return overallSimilarity >= 0.98;
    }
    
    /**
     * Check if images are considered very similar (above 95% similarity)
     */
    public boolean areVerySimilar() {
        return overallSimilarity >= 0.95;
    }
    
    /**
     * Check if images are considered similar (above 85% similarity)
     */
    public boolean areSimilar() {
        return overallSimilarity >= 0.85;
    }
}
