package com.maplecan.scraper.model;


import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Document(collection = "price_change")
@Data
@Builder
public class PriceChange {

    @Id
    private String id;
    private String url;
    private String imageUrl;
    private Double previousPrice;
    private Double newPrice;
    private LocalDateTime createdDate;
}
