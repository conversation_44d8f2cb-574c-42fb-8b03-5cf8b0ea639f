package com.maplecan.scraper.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

@Document(collection = "flyers")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Flyers {
    private String flyer;
    private String vendor;
    private LocalDateTime createdTimestamp;
    private LocalDateTime lastUpdatedTimestamp;
    private String validFrom;
    private String validTo;
    private List<CouponItem> items;
}
