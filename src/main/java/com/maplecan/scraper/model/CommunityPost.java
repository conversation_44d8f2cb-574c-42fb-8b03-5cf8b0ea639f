package com.maplecan.scraper.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Document(collection = "community_posts")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommunityPost {
    @Id
    private String id;
    
    @Indexed
    private String authorId; // Supabase user ID
    
    private String title;
    private String content;
    private String productUrl;
    private String productName;
    private String productImage;
    private Double originalPrice;
    private Double currentPrice;
    private String store; // costco, walmart, wayfair
    private String category; // hot-deals, price-drops, general
    
    @Indexed
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    // Engagement metrics
    @Builder.Default
    private Integer upvotes = 0;
    @Builder.Default
    private Integer downvotes = 0;
    @Builder.Default
    private Integer commentCount = 0;
    @Builder.Default
    private Integer viewCount = 0;

    // Lists to track who voted
    @Builder.Default
    private List<String> upvotedBy = new ArrayList<>(); // List of user IDs who upvoted
    @Builder.Default
    private List<String> downvotedBy = new ArrayList<>(); // List of user IDs who downvoted

    // Moderation
    @Builder.Default
    private Boolean isActive = true;
    @Builder.Default
    private Boolean isFeatured = false;
    @Builder.Default
    private List<String> tags = new ArrayList<>();

}
