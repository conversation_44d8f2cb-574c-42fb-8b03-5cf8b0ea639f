package com.maplecan.scraper.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@Document(collection = "receipt_data")
public class ReceiptData {

    @Id
    private String id; // MongoDB-generated ID
    private String receiptId; // Link to the original upload
    private String barcodeNumber;
    private String memberNumber;
    private String storeAddress;
    private String province;
    private Map<String, ItemDetail> items;
    private LocalDateTime processedTime;
}
