package com.maplecan.scraper.model;

import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Document(collection = "item")
@Data
@Builder
public class Item {

    @Id
    private String id;
    private String url;
    private String imageUrl;
    private String itemNumber;
    private LocalDateTime lastScanTime;
    private Double latestPrice;
    private String userEmailAdded;
    private LocalDateTime createdTimestamp;
    private LocalDateTime updatedTimestamp;
    private LocalDateTime lastPriceChangeTimestamp;
    private Boolean isAvailable;

}
