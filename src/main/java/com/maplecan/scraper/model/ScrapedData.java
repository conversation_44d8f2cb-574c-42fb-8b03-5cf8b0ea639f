package com.maplecan.scraper.model;

import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Document(collection = "scraped_data")
@Data
@Builder
public class ScrapedData {
    @Id
    private String id;
    private String url;
    private String imageUrl;
    private String itemNumber;
    private String email;
    private String productName;
    private Double price;
    private LocalDateTime emailTime;
    private LocalDateTime createdTimestamp;
    private LocalDateTime updatedTimestamp;
    private LocalDateTime lastScanTime;
    private Double userPrice;
    private String currencyCode;
    private String province;
    private Boolean isAvailable;
}

