package com.maplecan.scraper.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Document(collection = "community_comments")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommunityComment {
    @Id
    private String id;
    
    @Indexed
    private String postId; // Reference to CommunityPost
    
    @Indexed
    private String authorId; // Supabase user ID
    
    private String content;

    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    // Engagement metrics
    @Builder.Default
    private Integer upvotes = 0;
    @Builder.Default
    private Integer downvotes = 0;

    // Lists to track who voted
    @Builder.Default
    private List<String> upvotedBy = new ArrayList<>(); // List of user IDs who upvoted
    @Builder.Default
    private List<String> downvotedBy = new ArrayList<>(); // List of user IDs who downvoted

    // Threading support for nested comments
    private String parentCommentId; // null for top-level comments
    @Builder.Default
    private Integer depth = 0; // 0 for top-level, 1 for replies, etc.

    // Moderation
    @Builder.Default
    private Boolean isActive = true;

}
