package com.maplecan.scraper.model;

import lombok.Data;
import java.util.Optional;

@Data
public class CouponItem {
    private String productName;
    private String itemNumber;
    private String itemUrl;
    private String imageUrl;
    private boolean isOnline;
    private boolean warehouseOnly; // New field for Warehouse Only flag
    private String warehousePrice;
    private String ecoFee;
    private String instantSavings;
    private String price;
    private String validFrom;
    private String validTo;
}
