package com.maplecan.scraper.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Document(collection = "uploads")
public class UploadMetadata {
    @Id
    private String id;
    private String userId; // ID of the user who uploaded the file
    private String userEmail;
    private String filePath; // Path to the file in Supabase
    private String fileUrl; // Publicly accessible URL of the file
    private String fileType; // MIME type (e.g., image/jpeg, application/pdf)
    private LocalDateTime uploadTime; // Time of the upload
    private String status; // Status of the file processing (e.g., pending, completed)
}
