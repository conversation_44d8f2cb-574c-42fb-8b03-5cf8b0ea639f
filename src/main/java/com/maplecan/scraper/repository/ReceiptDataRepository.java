package com.maplecan.scraper.repository;

import com.maplecan.scraper.model.ReceiptData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ReceiptDataRepository extends MongoRepository<ReceiptData, String> {
    // Additional query methods if required
    Optional<ReceiptData> findByReceiptId(String receiptId);
    List<ReceiptData> findByProcessedTimeAfter(LocalDateTime date);
}
