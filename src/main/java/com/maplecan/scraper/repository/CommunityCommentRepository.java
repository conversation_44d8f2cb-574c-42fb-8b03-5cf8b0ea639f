package com.maplecan.scraper.repository;

import com.maplecan.scraper.model.CommunityComment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommunityCommentRepository extends MongoRepository<CommunityComment, String> {
    
    // Find comments for a specific post
    List<CommunityComment> findByPostIdAndIsActiveTrueOrderByCreatedAtAsc(String postId);
    
    // Find top-level comments for a post
    List<CommunityComment> findByPostIdAndParentCommentIdIsNullAndIsActiveTrueOrderByCreatedAtAsc(String postId);
    
    // Find replies to a specific comment
    List<CommunityComment> findByParentCommentIdAndIsActiveTrueOrderByCreatedAtAsc(String parentCommentId);
    
    // Find comments by author
    Page<CommunityComment> findByAuthorIdAndIsActiveTrueOrderByCreatedAtDesc(String authorId, Pageable pageable);
    
    // Count comments for a post
    long countByPostIdAndIsActiveTrue(String postId);
}
