package com.maplecan.scraper.repository;

import com.maplecan.scraper.model.Item;
import com.maplecan.scraper.model.ItemHistory;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.time.LocalDateTime;
import java.util.List;

public interface ItemHistoryRepository extends MongoRepository<ItemHistory, String> {
    /**
     * Find item history by URL and date range.
     *
     * @param url       The URL of the item.
     * @param startDate The start date of the range.
     * @param endDate   The end date of the range.
     * @return List of ItemHistory.
     */
    List<ItemHistory> findByUrlAndDateTimeBetween(String url, LocalDateTime startDate, LocalDateTime endDate);
}
