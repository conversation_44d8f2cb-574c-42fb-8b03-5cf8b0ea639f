package com.maplecan.scraper.repository;

import com.maplecan.scraper.model.UserMetadata;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserMetadataRepository extends MongoRepository<UserMetadata, String> {
    
    Optional<UserMetadata> findByEmail(String email);
    
    boolean existsByEmail(String email);
}
