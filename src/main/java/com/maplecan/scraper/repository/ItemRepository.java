package com.maplecan.scraper.repository;

import com.maplecan.scraper.model.Item;
import com.maplecan.scraper.model.ScrapedData;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface ItemRepository extends MongoRepository<Item, String> {
    // Custom query to find an item by its URL
    Optional<Item> findByUrl(String url);

    Optional<Item> findByItemNumber(String itemNumber);

    Optional<Item> findFirstByItemNumber(String itemNumber);

    // Check if an item with the given URL already exists globally
    boolean existsByUrl(String url);

    boolean existsByItemNumber(String itemNumber);

    /**
     * Finds all items added by a specific user.
     *
     * @param userEmailAdded The email of the user who added the items.
     * @return List of items added by the user.
     */
    List<Item> findByUserEmailAdded(String userEmailAdded);
}
