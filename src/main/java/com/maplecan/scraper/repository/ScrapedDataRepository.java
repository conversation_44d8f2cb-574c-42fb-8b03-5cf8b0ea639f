package com.maplecan.scraper.repository;

import com.maplecan.scraper.model.ScrapedData;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ScrapedDataRepository extends MongoRepository<ScrapedData, String> {

    List<ScrapedData> findByEmail(String email);
    void deleteById(ObjectId id);

}
