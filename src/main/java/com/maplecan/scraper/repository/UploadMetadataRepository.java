package com.maplecan.scraper.repository;

import com.maplecan.scraper.model.UploadMetadata;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UploadMetadataRepository extends MongoRepository<UploadMetadata, String> {
    List<UploadMetadata> findByUserId(String userId);
    List<UploadMetadata> findByStatus(String status);
}
