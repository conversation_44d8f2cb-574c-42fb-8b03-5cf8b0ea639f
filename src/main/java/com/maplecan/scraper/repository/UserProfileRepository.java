package com.maplecan.scraper.repository;

import com.maplecan.scraper.model.UserProfile;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserProfileRepository extends MongoRepository<UserProfile, String> {
    
    Optional<UserProfile> findBySupabaseUserId(String supabaseUserId);
    
    Optional<UserProfile> findByUsername(String username);
    
    boolean existsByUsername(String username);
    
    boolean existsBySupabaseUserId(String supabaseUserId);
}
