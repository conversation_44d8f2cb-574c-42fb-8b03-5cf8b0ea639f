package com.maplecan.scraper.repository;

import com.maplecan.scraper.model.CommunityPost;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface CommunityPostRepository extends MongoRepository<CommunityPost, String> {
    
    // Find active posts ordered by creation date
    Page<CommunityPost> findByIsActiveTrueOrderByCreatedAtDesc(Pageable pageable);
    
    // Find posts by store
    Page<CommunityPost> findByStoreAndIsActiveTrueOrderByCreatedAtDesc(String store, Pageable pageable);
    
    // Find posts by category
    Page<CommunityPost> findByCategoryAndIsActiveTrueOrderByCreatedAtDesc(String category, Pageable pageable);
    
    // Find hot deals (high engagement in last 24 hours)
    @Query("{ 'isActive': true, 'createdAt': { $gte: ?0 } }")
    List<CommunityPost> findHotDeals(LocalDateTime since);
    
    // Find featured posts
    Page<CommunityPost> findByIsFeaturedTrueAndIsActiveTrueOrderByCreatedAtDesc(Pageable pageable);
    
    // Find posts by author
    Page<CommunityPost> findByAuthorIdAndIsActiveTrueOrderByCreatedAtDesc(String authorId, Pageable pageable);
    
    // Find trending posts (high upvotes)
    @Query("{ 'isActive': true }")
    Page<CommunityPost> findTrendingPosts(Pageable pageable);
}
