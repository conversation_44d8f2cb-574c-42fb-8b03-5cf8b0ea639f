package com.maplecan.scraper.repository;

import com.maplecan.scraper.model.ItemHistory;
import com.maplecan.scraper.model.PriceChange;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.time.LocalDateTime;
import java.util.List;

public interface PriceChangeRepository extends MongoRepository<PriceChange, String> {
    /**
     * Find item history by URL and date range.
     *
     *
     * @param startDate The start date of the range.
     * @param endDate   The end date of the range.
     * @return List of ItemHistory.
     */
    List<PriceChange> findByCreatedDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    List<PriceChange> findByCreatedDateBetweenOrderByCreatedDateDesc(LocalDateTime startDate, LocalDateTime endDate);
}