-- Create RLS policies for storage.objects table to allow authenticated users to upload screenshots

-- Policy to allow authenticated users to upload files to the 'items' bucket
-- Files must be organized in user-specific folders (userId/filename)
CREATE POLICY "Allow authenticated users to upload screenshots"
ON storage.objects
FOR INSERT
TO authenticated
WITH CHECK (
    bucket_id = 'items' 
    AND (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy to allow authenticated users to read their own uploaded files
CREATE POLICY "Allow authenticated users to read their own screenshots"
ON storage.objects
FOR SELECT
TO authenticated
USING (
    bucket_id = 'items' 
    AND (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy to allow authenticated users to update/overwrite their own files
CREATE POLICY "Allow authenticated users to update their own screenshots"
ON storage.objects
FOR UPDATE
TO authenticated
USING (
    bucket_id = 'items' 
    AND (storage.foldername(name))[1] = (SELECT auth.uid()::text)
)
WITH CHECK (
    bucket_id = 'items' 
    AND (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Policy to allow authenticated users to delete their own files
CREATE POLICY "Allow authenticated users to delete their own screenshots"
ON storage.objects
FOR DELETE
TO authenticated
USING (
    bucket_id = 'items' 
    AND (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);
