@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    color-scheme: dark;
  }

  html {
    @apply antialiased text-gray-100 bg-dark;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }

  body {
    @apply min-h-screen;
  }
}

@layer components {
  .gradient-bg {
    @apply bg-gradient-to-br from-dark to-dark-lighter;
  }

  .btn-primary {
    @apply px-4 py-2 bg-primary text-white rounded-lg 
           hover:bg-primary-dark transition-all duration-200
           shadow-md hover:shadow-lg hover:shadow-primary/20;
  }

  .btn-secondary {
    @apply px-4 py-2 bg-dark-lighter text-gray-100 rounded-lg
           hover:bg-gray-700 transition-all duration-200
           border border-gray-700 hover:border-gray-600;
  }

  /* Custom scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #4b5563 #1f2937;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #1f2937;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
  }

  .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
    background: #4b5563;
  }

  .scrollbar-track-gray-800::-webkit-scrollbar-track {
    background: #1f2937;
  }
}

/* Inter font */
@import '@fontsource/inter/variable.css';

/* JetBrains Mono font */
@import '@fontsource/jetbrains-mono/variable.css';