{"name": "vite-svelte-ts-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.json"}, "dependencies": {"@fontsource/inter": "^5.0.17", "@fontsource/jetbrains-mono": "^5.0.19", "@lottiefiles/dotlottie-svelte": "^0.4.6", "@microsoft/clarity": "^1.0.0", "@supabase/supabase-js": "^2.39.7", "chart.js": "^4.4.1", "dompurify": "^3.0.9", "lucide-svelte": "^0.359.0", "marked": "^12.0.0"}, "devDependencies": {"@rollup/plugin-replace": "^5.0.5", "@sveltejs/vite-plugin-svelte": "^3.1.2", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.10", "@tsconfig/svelte": "^5.0.4", "autoprefixer": "^10.4.18", "postcss": "^8.4.35", "svelte": "^4.2.19", "svelte-check": "^3.8.6", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "tslib": "^2.7.0", "typescript": "^5.5.3", "vite": "^5.4.2", "vite-plugin-compression": "^0.5.1"}}