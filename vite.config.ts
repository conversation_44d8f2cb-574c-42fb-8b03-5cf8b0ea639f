import { defineConfig, loadEnv } from 'vite';
import { svelte } from '@sveltejs/vite-plugin-svelte';
import replace from '@rollup/plugin-replace';
import viteCompression from 'vite-plugin-compression';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const apiUrl = env.VITE_API_URL || 'http://localhost:8080';

  return {
    plugins: [
      svelte(),
      replace({
        preventAssignment: true,
        'process.env.VITE_API_URL': JSON.stringify(apiUrl)
      }),
      // Enable gzip compression
      viteCompression({
        algorithm: 'gzip',
        ext: '.gz'
      }),
      // Enable brotli compression (better than gzip)
      viteCompression({
        algorithm: 'brotliCompress',
        ext: '.br'
      })
    ],
    build: {
      // Remove unused JavaScript through tree shaking
      rollupOptions: {
        output: {
          manualChunks: {
            // Separate vendor chunks to improve caching
            vendor: ['svelte'],
            utils: ['lucide-svelte']
          }
        }
      },
      // Enable minification
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true, // Remove console.logs in production
          drop_debugger: true
        }
      },
      // Optimize chunk size
      chunkSizeWarningLimit: 1000
    },
    server: {
      proxy: {
        '/api': {
          target: apiUrl,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    }
  };
});