# AI-Powered Price Drop Detection

This document explains the AI-powered price drop detection system that integrates OpenAI's GPT-4 Vision API with BargainHawk's screenshot comparison workflow.

## Overview

The system automatically:
1. Takes daily screenshots of tracked websites
2. Compares new screenshots with baseline images
3. When changes are detected, sends both images to OpenAI's GPT-4 Vision API
4. AI analyzes the images for price drops with confidence levels
5. Sends email notifications to users when price drops are detected

## Implementation Details

### Direct OpenAI API Integration

We use **direct HTTP calls** to OpenAI's API instead of third-party libraries for better security and reliability:

- **Model**: `gpt-4o-mini` (cost-effective vision model)
- **Endpoint**: `https://api.openai.com/v1/chat/completions`
- **Method**: POST with JSON payload
- **Authentication**: Bearer token in Authorization header

### API Request Format

```json
{
  "model": "gpt-4o-mini",
  "messages": [
    {
      "role": "system",
      "content": "You are an expert e-commerce price monitoring assistant..."
    },
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Please analyze these two screenshots for price changes..."
        },
        {
          "type": "image_url",
          "image_url": {"url": "https://example.com/baseline.jpg"}
        },
        {
          "type": "image_url",
          "image_url": {"url": "https://example.com/new.jpg"}
        }
      ]
    }
  ],
  "max_tokens": 500,
  "temperature": 0.1
}
```

## Configuration

### Environment Variables

Set the OpenAI API key in your environment:

```bash
export OPENAI_API_KEY="sk-proj-your-api-key-here"
```

### Application Properties

```properties
# OpenAI API Configuration
openai.api.key=${OPENAI_API_KEY:}
openai.api.url=https://api.openai.com/v1
```

## Testing

### Test Endpoints

1. **Health Check**
   ```
   GET /api/test/ai/health
   ```

2. **Test Image Accessibility**
   ```
   POST /api/test/ai/test-images
   Content-Type: application/json

   {
     "baselineImageUrl": "https://your-supabase.supabase.co/storage/v1/object/public/items/image1.jpg",
     "newImageUrl": "https://your-supabase.supabase.co/storage/v1/object/public/items/image2.jpg"
   }
   ```
   Tests if OpenAI can access your images before analysis.

3. **Manual Analysis**
   ```
   POST /api/test/ai/analyze-price-drop
   Content-Type: application/json

   {
     "baselineImageUrl": "https://example.com/baseline.jpg",
     "newImageUrl": "https://example.com/new.jpg",
     "websiteUrl": "https://example.com/product"
   }
   ```

4. **Debug API Request**
   ```
   POST /api/test/ai/debug-request
   ```
   Shows the exact API request format sent to OpenAI.

5. **Sample Data**
   ```
   GET /api/test/ai/sample-data
   ```
   Returns sample test data and instructions.

### Common Issues

#### Supabase Image Access
If you get responses like "I cannot extract any price information from either image", the issue is likely that OpenAI cannot access your Supabase images:

**Problem**: Your Supabase storage URLs might require authentication or have CORS restrictions.

**Solutions**:
1. **Make images publicly accessible** in Supabase:
   ```sql
   -- In Supabase SQL editor
   UPDATE storage.buckets
   SET public = true
   WHERE id = 'items';
   ```

2. **Check CORS settings** in Supabase Dashboard:
   - Go to Storage > Settings
   - Add OpenAI domains to allowed origins

3. **Test image accessibility** first:
   ```bash
   curl -I "https://your-supabase-url/storage/v1/object/public/items/image.jpg"
   ```

4. **Use the test endpoint**:
   ```
   POST /api/test/ai/test-images
   ```
   This will tell you if the images are accessible.

### Manual Testing with cURL

You can test the OpenAI API directly:

```bash
curl https://api.openai.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $OPENAI_API_KEY" \
  -d '{
    "model": "gpt-4o-mini",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "What do you see in this image?"
          },
          {
            "type": "image_url",
            "image_url": {"url": "https://example.com/image.jpg"}
          }
        ]
      }
    ],
    "max_tokens": 300
  }'
```

## Workflow Integration

### Daily Screenshot Job

The `ScreenshotComparisonJob` runs daily at 2:00 AM UTC:

1. **Image Comparison**: Uses existing algorithms to detect visual changes
2. **AI Analysis**: When changes detected, calls `OpenAIVisionService.analyzePriceDrop()`
3. **Email Notification**: Sends alerts when AI confidence > 70%
4. **Data Storage**: Saves AI analysis results in MongoDB

### Email Notifications

When AI detects a price drop:
- **Subject**: "Price Drop Alert! Website Change Detected - [domain]"
- **Content**: Professional HTML email with AI analysis details
- **Confidence**: Shows AI confidence level and reasoning
- **Actions**: Links to view website and dashboard

## Key Features

### Smart Filtering
- Only sends emails when AI confidence > 70%
- Prevents false positives from minor layout changes
- Includes AI reasoning in notifications

### Error Handling
- Graceful fallback when API key not configured
- Comprehensive error logging
- Continues normal operation if AI analysis fails

### Cost Optimization
- Uses `gpt-4o-mini` model for cost efficiency
- Only analyzes images when visual changes detected
- Configurable confidence thresholds

## Monitoring

### Logs
- AI analysis results logged with confidence levels
- API call success/failure tracking
- Email notification status

### Database
- AI analysis results stored in `ChangeDetection` records
- Confidence levels and reasoning preserved
- Historical analysis data for debugging

## Security

- No third-party libraries for OpenAI integration
- Direct API calls with proper authentication
- API key stored as environment variable
- No sensitive data in logs or responses

## Documentation

- **OpenAI Vision API**: https://platform.openai.com/docs/guides/images-vision
- **Chat Completions**: https://platform.openai.com/docs/api-reference/chat/create
- **Image Inputs**: https://platform.openai.com/docs/guides/images-vision#image-inputs
