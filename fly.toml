# fly.toml app configuration file generated for scraper-wild-dawn-9932 on 2024-12-08T20:42:32-04:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'scraper-wild-dawn-9932'
primary_region = 'ord'

[build]

[env]
  # JVM Memory Configuration for large image processing
  JAVA_OPTS = "-Xmx768m -Xms256m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 1
  max_machines_running = 1
  processes = ['app']

[[vm]]
  memory = '1gb'  # Increased from 1gb for better image processing
  cpu_kind = 'shared'
  cpus = 1
