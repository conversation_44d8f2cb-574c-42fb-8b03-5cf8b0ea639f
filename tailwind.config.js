/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{html,js,svelte,ts}'],
  theme: {
    extend: {
      colors: {
        // <PERSON><PERSON>'s color palette
        primary: {
          DEFAULT: '#0EA5E9', // Bright blue
          dark: '#0284C7',
          light: '#38BDF8'
        },
        dark: {
          DEFAULT: '#0F172A', // Navy blue
          lighter: '#1E293B',
          navbar: 'rgba(15, 23, 42, 0.9)'
        },
        gray: {
          50: '#F8FAFC',
          100: '#F1F5F9',
          200: '#E2E8F0',
          300: '#CBD5E1',
          400: '#94A3B8',
          500: '#64748B',
          600: '#475569',
          700: '#334155',
          800: '#1E293B',
          900: '#0F172A'
        }
      },
      fontFamily: {
        sans: ['Inter var', 'system-ui', '-apple-system', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace']
      },
      boxShadow: {
        'glow': '0 0 20px -5px rgba(14, 165, 233, 0.3)',
      }
    }
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/line-clamp')
  ]
}