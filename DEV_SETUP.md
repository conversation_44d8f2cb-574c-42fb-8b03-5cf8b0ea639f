# Development Environment Setup

This document explains how to set up and use the development environment configurations for both the frontend and backend.

## Frontend Development

### Environment Files

- **`.env`** - Currently configured for development (points to localhost:8080)
- **`.env.dev`** - Dedicated development environment file

### Configuration

The development environment uses:
- **Supabase URL**: `https://tovaxwkyjnrfssqzpmcl.supabase.co`
- **API URL**: `http://localhost:8080` (local backend)

### Usage

1. Make sure the `.env` file is configured for development (already done)
2. Start the frontend: `npm run dev`
3. The app will run on an available port (usually 5173, 5174, 5175, 5176, or 5177)

## Backend Development

### Environment Files

- **`application.properties`** - Main configuration (production-ready)
- **`application-dev.properties`** - Development-specific configuration

### Configuration

The development profile (`application-dev.properties`) includes:
- **Local MongoDB**: `localhost:27017` (database: `scraping`)
- **Development Supabase**: JWT secret and URL for dev instance
- **CORS**: Allows localhost ports 5173-5177
- **ScrapingBee API**: Development API key
- **Mailjet**: Development email configuration

### Usage

#### Option 1: Run with development profile
```bash
cd /Users/<USER>/Downloads/scraper
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
```

#### Option 2: Set active profile in IDE
Add VM option: `-Dspring.profiles.active=dev`

#### Option 3: Environment variable
```bash
export SPRING_PROFILES_ACTIVE=dev
./mvnw spring-boot:run
```

## Prerequisites

### Local Development Requirements

1. **MongoDB**: Running on `localhost:27017`
   ```bash
   # Start MongoDB (if using Homebrew)
   brew services start mongodb-community
   ```

2. **Java 21**: Required for the Spring Boot application

3. **Node.js**: Required for the frontend

## Development Workflow

1. **Start MongoDB**: `brew services start mongodb-community`
2. **Start Backend**: `cd scraper && ./mvnw spring-boot:run -Dspring-boot.run.profiles=dev`
3. **Start Frontend**: `cd project && npm run dev`
4. **Access Application**: Open browser to the frontend URL (usually http://localhost:5177)

## Environment Variables Summary

### Frontend (.env.dev)
```
VITE_SUPABASE_URL=https://tovaxwkyjnrfssqzpmcl.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
VITE_API_URL=http://localhost:8080
```

### Backend (application-dev.properties)
- Local MongoDB connection
- Development Supabase configuration
- CORS enabled for localhost ports
- All necessary API keys and credentials

## Notes

- The main `application.properties` file has been updated to include CORS support for port 5177
- The development profile uses local MongoDB instead of the cloud instance
- All API keys and credentials are configured for the development environment
- The backend supports PATCH requests for the screenshot coordinate updates
